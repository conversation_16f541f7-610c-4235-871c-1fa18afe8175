import 'package:flutter/material.dart';
import '../core/widget_json_converter.dart';
import '../exceptions/widget_conversion_exception.dart';

/// Central registry service for managing widget converters
/// 
/// This service maintains a registry of all available widget converters
/// and provides methods to register new converters and retrieve them by widget type.
class WidgetRegistryService {
  static final WidgetRegistryService _instance = WidgetRegistryService._internal();
  factory WidgetRegistryService() => _instance;
  WidgetRegistryService._internal();

  /// Map of widget type to converter
  final Map<String, WidgetJsonConverter> _converters = {};

  /// Map of widget runtime type to widget type string
  final Map<Type, String> _typeMapping = {};

  /// Registers a widget converter
  /// 
  /// [converter] The converter to register
  /// Throws [WidgetConversionException] if a converter for this type is already registered
  void register<T extends Widget>(WidgetJsonConverter<T> converter) {
    final widgetType = converter.widgetType;
    
    if (_converters.containsKey(widgetType)) {
      throw WidgetConversionException(
        'Converter for widget type "$widgetType" is already registered',
        widgetType: widgetType,
      );
    }
    
    _converters[widgetType] = converter;
    _typeMapping[T] = widgetType;
  }

  /// Unregisters a widget converter
  /// 
  /// [widgetType] The widget type to unregister
  void unregister(String widgetType) {
    final converter = _converters.remove(widgetType);
    if (converter != null) {
      // Remove from type mapping
      _typeMapping.removeWhere((type, typeString) => typeString == widgetType);
    }
  }

  /// Gets a converter by widget type string
  /// 
  /// [widgetType] The widget type string
  /// Returns the converter or null if not found
  WidgetJsonConverter? getConverter(String widgetType) {
    return _converters[widgetType];
  }

  /// Gets a converter by widget runtime type
  /// 
  /// [widgetRuntimeType] The runtime type of the widget
  /// Returns the converter or null if not found
  WidgetJsonConverter? getConverterByType(Type widgetRuntimeType) {
    final widgetType = _typeMapping[widgetRuntimeType];
    if (widgetType == null) return null;
    return _converters[widgetType];
  }

  /// Gets a converter for a specific widget instance
  /// 
  /// [widget] The widget instance
  /// Returns the converter or null if not found
  WidgetJsonConverter? getConverterForWidget(Widget widget) {
    return getConverterByType(widget.runtimeType);
  }

  /// Checks if a widget type is registered
  /// 
  /// [widgetType] The widget type string to check
  /// Returns true if registered, false otherwise
  bool isRegistered(String widgetType) {
    return _converters.containsKey(widgetType);
  }

  /// Checks if a widget runtime type is registered
  /// 
  /// [widgetRuntimeType] The runtime type to check
  /// Returns true if registered, false otherwise
  bool isTypeRegistered(Type widgetRuntimeType) {
    return _typeMapping.containsKey(widgetRuntimeType);
  }

  /// Gets all registered widget types
  /// 
  /// Returns a list of all registered widget type strings
  List<String> getRegisteredTypes() {
    return _converters.keys.toList();
  }

  /// Gets all registered converters
  /// 
  /// Returns a map of widget type to converter
  Map<String, WidgetJsonConverter> getAllConverters() {
    return Map.unmodifiable(_converters);
  }

  /// Clears all registered converters
  /// 
  /// This is mainly useful for testing
  void clear() {
    _converters.clear();
    _typeMapping.clear();
  }

  /// Gets the widget type string for a runtime type
  /// 
  /// [widgetRuntimeType] The runtime type
  /// Returns the widget type string or null if not found
  String? getWidgetType(Type widgetRuntimeType) {
    return _typeMapping[widgetRuntimeType];
  }

  /// Validates that all required converters are registered
  /// 
  /// [requiredTypes] List of widget types that must be registered
  /// Returns true if all are registered, false otherwise
  /// [missingTypes] will be populated with any missing types
  bool validateRegistration(List<String> requiredTypes, {List<String>? missingTypes}) {
    missingTypes?.clear();
    bool allRegistered = true;
    
    for (final type in requiredTypes) {
      if (!isRegistered(type)) {
        allRegistered = false;
        missingTypes?.add(type);
      }
    }
    
    return allRegistered;
  }

  /// Gets statistics about registered converters
  /// 
  /// Returns a map with registration statistics
  Map<String, dynamic> getStats() {
    return {
      'totalConverters': _converters.length,
      'registeredTypes': getRegisteredTypes(),
      'typeMappings': _typeMapping.length,
    };
  }
}
