import 'package:flutter/material.dart';
import 'package:bi_directional_widget/bi_directional_widget.dart';
import 'dart:convert';

/// Demo app comparing Custom Serialization vs Runtime Property Discovery
class RuntimeComparisonDemo extends StatefulWidget {
  const RuntimeComparisonDemo({Key? key}) : super(key: key);

  @override
  State<RuntimeComparisonDemo> createState() => _RuntimeComparisonDemoState();
}

class _RuntimeComparisonDemoState extends State<RuntimeComparisonDemo> {
  // Custom serialization approach (your current implementation)
  CalendarWidget? _customCalendar;
  String _customJsonOutput = 'Click "Generate Custom JSON" to see output';
  
  // Runtime discovery approach
  RuntimeCalendarWidget? _runtimeCalendar;
  String _runtimeJsonOutput = 'Click "Generate Runtime JSON" to see output';

  // Recreated widget from JSON
  RuntimeCalendarWidget? _recreatedFromJson;
  
  // Comparison stats
  Map<String, dynamic> _comparisonStats = {};

  @override
  void initState() {
    super.initState();
    _createCalendars();
  }

  void _createCalendars() {
    // Create identical calendars with both approaches
    final commonConfig = {
      'size': CalendarSize.medium,
      'initialSelectedDate': DateTime.now(),
      'borderRadius': 16.0,
      'borderWidth': 1.0,
      'hasBorder': true,
      'hasShadow': true,
      'selectedDateColor': Colors.blue,
      'todayColor': Colors.orange.shade100,
      'headerColor': Colors.blue.shade50,
    };

    setState(() {
      // Custom serialization calendar
      _customCalendar = CalendarWidget(
        size: commonConfig['size'] as CalendarSize,
        initialSelectedDate: commonConfig['initialSelectedDate'] as DateTime,
        borderRadius: commonConfig['borderRadius'] as double,
        borderWidth: commonConfig['borderWidth'] as double,
        hasBorder: commonConfig['hasBorder'] as bool,
        hasShadow: commonConfig['hasShadow'] as bool,
        selectedDateColor: commonConfig['selectedDateColor'] as Color,
        todayColor: commonConfig['todayColor'] as Color,
        headerColor: commonConfig['headerColor'] as Color,
        onDateSelected: (date) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Custom: Selected ${date.toString().split(' ')[0]}')),
          );
        },
      );

      // Runtime discovery calendar
      _runtimeCalendar = RuntimeCalendarWidget(
        size: commonConfig['size'] as CalendarSize,
        initialSelectedDate: commonConfig['initialSelectedDate'] as DateTime,
        borderRadius: commonConfig['borderRadius'] as double,
        borderWidth: commonConfig['borderWidth'] as double,
        hasBorder: commonConfig['hasBorder'] as bool,
        hasShadow: commonConfig['hasShadow'] as bool,
        selectedDateColor: commonConfig['selectedDateColor'] as Color,
        todayColor: commonConfig['todayColor'] as Color,
        headerColor: commonConfig['headerColor'] as Color,
        onDateSelected: (date) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Runtime: Selected ${date.toString().split(' ')[0]}')),
          );
        },
      );
    });
  }

  void _generateCustomJson() {
    if (_customCalendar != null) {
      final startTime = DateTime.now().microsecondsSinceEpoch;
      final json = _customCalendar!.toJson();
      final endTime = DateTime.now().microsecondsSinceEpoch;
      
      setState(() {
        _customJsonOutput = _customCalendar!.toJsonString(prettyPrint: true);
        _comparisonStats['customSerializationTime'] = endTime - startTime;
        _comparisonStats['customJsonSize'] = _customJsonOutput.length;
        _comparisonStats['customPropertyCount'] = json.keys.length;
      });
    }
  }

  void _generateRuntimeJson() {
    if (_runtimeCalendar != null) {
      final startTime = DateTime.now().microsecondsSinceEpoch;
      final json = _runtimeCalendar!.toRuntimeJson();
      final endTime = DateTime.now().microsecondsSinceEpoch;
      
      final stats = _runtimeCalendar!.getSerializationStats();
      
      setState(() {
        _runtimeJsonOutput = _runtimeCalendar!.toRuntimeJsonString(prettyPrint: true);
        _comparisonStats['runtimeSerializationTime'] = endTime - startTime;
        _comparisonStats['runtimeJsonSize'] = _runtimeJsonOutput.length;
        _comparisonStats['runtimePropertyCount'] = json.keys.length;
        _comparisonStats['runtimeStats'] = stats;
      });
    }
  }

  void _generateBothJson() {
    _generateCustomJson();
    _generateRuntimeJson();
  }

  void _testRuntimeRoundTrip() {
    if (_runtimeCalendar != null) {
      try {
        // Step 1: Serialize to JSON
        final originalJson = _runtimeCalendar!.toRuntimeJsonString(prettyPrint: true);
        final originalData = jsonDecode(originalJson);

        // Step 2: Deserialize back to widget
        final recreatedWidget = RuntimeCalendarWidget.fromRuntimeJsonString(originalJson);

        // Step 3: Serialize again to verify
        final recreatedJson = recreatedWidget.toRuntimeJsonString(prettyPrint: true);
        final recreatedData = jsonDecode(recreatedJson);

        // Step 4: Compare meaningful properties (ignore runtime-generated ones)
        final meaningfulComparison = _compareMeaningfulProperties(originalData, recreatedData);

        setState(() {
          // Store the recreated widget to display it
          _recreatedFromJson = recreatedWidget;

          _runtimeJsonOutput = 'ROUND-TRIP CONVERSION SUCCESSFUL!\n\n'
              '✅ Widget recreated from JSON and displayed below\n\n'
              '${meaningfulComparison['summary']}\n\n'
              'JSON used for recreation:\n$originalJson';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(meaningfulComparison['isSuccess']
                ? '✅ Runtime round-trip conversion successful!'
                : '⚠️ Some properties differ in round-trip'),
            backgroundColor: meaningfulComparison['isSuccess'] ? Colors.green : Colors.orange,
          ),
        );
      } catch (e) {
        setState(() {
          _runtimeJsonOutput = 'ROUND-TRIP TEST FAILED!\n\nError: $e';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Runtime round-trip failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Compare meaningful properties between original and recreated JSON
  Map<String, dynamic> _compareMeaningfulProperties(Map<String, dynamic> original, Map<String, dynamic> recreated) {
    // Properties to ignore (runtime-generated)
    final ignoreProperties = {'runtimeType', 'key', 'hashCode'};

    // Properties that matter for widget functionality
    final meaningfulProperties = [
      'type', 'size', 'initialSelectedDate', 'borderRadius', 'borderWidth',
      'hasBorder', 'hasShadow', 'backgroundColor', 'headerColor',
      'selectedDateColor', 'todayColor', 'headerTextColor', 'weekdayLabelColor',
      'dateTextColor', 'width', 'height', 'showHeader', 'showNavigation',
      'showWeekdayLabels', 'allowDateSelection', 'allowMultipleSelection',
      'firstDayOfWeek', 'events'
    ];

    List<String> matches = [];
    List<String> differences = [];

    for (String property in meaningfulProperties) {
      final originalValue = original[property];
      final recreatedValue = recreated[property];

      if (originalValue == recreatedValue) {
        matches.add('✅ $property: $originalValue');
      } else {
        differences.add('⚠️ $property: $originalValue → $recreatedValue');
      }
    }

    // Check for extra properties in recreated (excluding ignored ones)
    final extraProperties = recreated.keys
        .where((key) => !original.containsKey(key) && !ignoreProperties.contains(key))
        .toList();

    String summary;
    bool isSuccess;

    if (differences.isEmpty && extraProperties.isEmpty) {
      summary = '🎉 PERFECT MATCH! All meaningful properties preserved.';
      isSuccess = true;
    } else if (differences.isEmpty) {
      summary = '✅ FUNCTIONAL MATCH! Core properties preserved (${extraProperties.length} extra runtime properties added).';
      isSuccess = true;
    } else {
      summary = '⚠️ PARTIAL MATCH! ${differences.length} properties differ, ${matches.length} match.';
      isSuccess = false;
    }

    String details = '';
    if (matches.isNotEmpty) {
      details += 'Matching Properties (${matches.length}):\n${matches.take(5).join('\n')}';
      if (matches.length > 5) details += '\n... and ${matches.length - 5} more';
    }

    if (differences.isNotEmpty) {
      details += '\n\nDiffering Properties (${differences.length}):\n${differences.join('\n')}';
    }

    if (extraProperties.isNotEmpty) {
      details += '\n\nExtra Runtime Properties: ${extraProperties.join(', ')}';
    }

    return {
      'isSuccess': isSuccess,
      'summary': summary,
      'details': details,
      'matchCount': matches.length,
      'differenceCount': differences.length,
      'extraCount': extraProperties.length,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Serialization Approach Comparison'),
        backgroundColor: Colors.blue.shade100,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Control buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: _generateCustomJson,
                  child: const Text('Generate Custom JSON'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _generateRuntimeJson,
                  child: const Text('Generate Runtime JSON'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _generateBothJson,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  child: const Text('Compare Both'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _testRuntimeRoundTrip,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                  child: const Text('Test Round-Trip'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _recreatedFromJson = null;
                      _runtimeJsonOutput = 'Click "Generate Runtime JSON" to see output';
                    });
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                  child: const Text('Clear'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Comparison stats
            if (_comparisonStats.isNotEmpty) _buildComparisonStats(),
            
            const SizedBox(height: 24),
            
            // Three-column comparison
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Custom serialization side
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🎯 Custom Serialization (Your Current Approach)',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      if (_customCalendar != null)
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.blue),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _customCalendar!,
                        ),
                      
                      const SizedBox(height: 16),
                      
                      const Text(
                        'Generated JSON:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 300,
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: SingleChildScrollView(
                          child: SelectableText(
                            _customJsonOutput,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Runtime discovery side
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🔍 Runtime Property Discovery',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      if (_runtimeCalendar != null)
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.orange),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _runtimeCalendar!,
                        ),

                      const SizedBox(height: 16),

                      const Text(
                        'Generated JSON:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: SingleChildScrollView(
                          child: SelectableText(
                            _runtimeJsonOutput,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Recreated widget from JSON side
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🔄 Widget Recreated from JSON',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      if (_recreatedFromJson != null)
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.green, width: 2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _recreatedFromJson!,
                        )
                      else
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.widgets, size: 48, color: Colors.grey),
                                SizedBox(height: 8),
                                Text(
                                  'Click "Test Round-Trip"\nto recreate widget from JSON',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),

                      const SizedBox(height: 16),

                      const Text(
                        'Proof of Concept:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '✅ This proves bidirectional conversion:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            const Text('1. Widget → JSON (serialization)'),
                            const Text('2. JSON → Widget (deserialization)'),
                            const Text('3. Visual proof: Widget displays correctly'),
                            const SizedBox(height: 8),
                            if (_recreatedFromJson != null)
                              const Text(
                                '🎉 Success! The widget above was created entirely from the JSON data.',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            else
                              const Text(
                                'Click "Test Round-Trip" to see the magic happen!',
                                style: TextStyle(color: Colors.grey),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '📊 Performance Comparison',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Custom Serialization',
                  [
                    'Time: ${_comparisonStats['customSerializationTime'] ?? 'N/A'} μs',
                    'JSON Size: ${_comparisonStats['customJsonSize'] ?? 'N/A'} chars',
                    'Properties: ${_comparisonStats['customPropertyCount'] ?? 'N/A'}',
                  ],
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Runtime Discovery',
                  [
                    'Time: ${_comparisonStats['runtimeSerializationTime'] ?? 'N/A'} μs',
                    'JSON Size: ${_comparisonStats['runtimeJsonSize'] ?? 'N/A'} chars',
                    'Properties: ${_comparisonStats['runtimePropertyCount'] ?? 'N/A'}',
                    if (_comparisonStats['runtimeStats'] != null)
                      'Support Rate: ${(_comparisonStats['runtimeStats']['supportRate'] ?? 0).toStringAsFixed(1)}%',
                  ],
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, List<String> stats, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 8),
          ...stats.map((stat) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              stat,
              style: const TextStyle(fontSize: 12),
            ),
          )),
        ],
      ),
    );
  }
}
