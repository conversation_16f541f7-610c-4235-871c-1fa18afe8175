import 'package:flutter/material.dart';
import 'dart:convert';
import '../core/json_serializable_widget.dart';
import '../utils/color_utils.dart';

/// Size options for the calendar widget
enum CalendarSize {
  /// Small calendar with compact layout
  small,
  
  /// Medium calendar with standard layout  
  medium,
  
  /// Large calendar with expanded layout
  large,
}

/// Calendar event model
class CalendarEvent {
  final String title;
  final String? description;
  final Color? color;
  final DateTime? startTime;
  final DateTime? endTime;

  const CalendarEvent({
    required this.title,
    this.description,
    this.color,
    this.startTime,
    this.endTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'color': ColorUtils.colorToJson(color),
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
    };
  }

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      title: json['title'] as String,
      description: json['description'] as String?,
      color: ColorUtils.jsonToColor(json['color']),
      startTime: json['startTime'] != null ? DateTime.parse(json['startTime']) : null,
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
    );
  }
}

/// A comprehensive calendar widget with support for different sizes and extensive customization.
///
/// This widget provides a full calendar view with month navigation, date selection,
/// and support for events. It can be configured in three sizes (small, medium, large)
/// with different typography, border, and shadow properties.
class CalendarWidget extends StatefulWidget implements JsonSerializableWidget {
  /// The size of the calendar (small, medium, large)
  final CalendarSize size;
  
  /// The initially selected date
  final DateTime? initialSelectedDate;
  
  /// The currently displayed month/year
  final DateTime? initialDisplayDate;
  
  /// Whether to allow date selection
  final bool allowDateSelection;
  
  /// Whether to allow multiple date selection
  final bool allowMultipleSelection;
  
  /// List of initially selected dates (for multiple selection)
  final List<DateTime>? initialSelectedDates;
  
  /// Minimum selectable date
  final DateTime? minDate;
  
  /// Maximum selectable date  
  final DateTime? maxDate;
  
  /// Events to display on the calendar
  final Map<DateTime, List<CalendarEvent>>? events;
  
  /// Whether to show the month/year header
  final bool showHeader;
  
  /// Whether to show navigation arrows
  final bool showNavigation;
  
  /// Whether to show weekday labels
  final bool showWeekdayLabels;
  
  /// First day of the week (0 = Sunday, 1 = Monday, etc.)
  final int firstDayOfWeek;
  
  /// Locale for date formatting
  final String locale;
  
  // Typography properties based on size
  /// Heading font size (varies by size)
  final double? headingFontSize;
  
  /// Body font size (varies by size)  
  final double? bodyFontSize;
  
  /// Label font size (varies by size)
  final double? labelFontSize;
  
  /// Font weight for heading
  final FontWeight? headingFontWeight;
  
  /// Font weight for body text
  final FontWeight? bodyFontWeight;
  
  /// Font weight for labels
  final FontWeight? labelFontWeight;
  
  // Border properties
  /// Border width (varies by size)
  final double? borderWidth;
  
  /// Border color
  final Color? borderColor;
  
  /// Border radius (varies by size)
  final double? borderRadius;
  
  /// Whether to show border
  final bool hasBorder;
  
  // Shadow properties  
  /// Box shadow configuration
  final List<BoxShadow>? boxShadow;
  
  /// Whether to show shadow
  final bool hasShadow;
  
  // Color properties
  /// Background color of the calendar
  final Color? backgroundColor;
  
  /// Color of the header
  final Color? headerColor;
  
  /// Text color for header
  final Color? headerTextColor;
  
  /// Color of selected dates
  final Color? selectedDateColor;
  
  /// Text color for selected dates
  final Color? selectedDateTextColor;
  
  /// Color of today's date
  final Color? todayColor;
  
  /// Text color for today's date
  final Color? todayTextColor;
  
  /// Color of weekday labels
  final Color? weekdayLabelColor;
  
  /// Color of regular dates
  final Color? dateColor;
  
  /// Text color for regular dates
  final Color? dateTextColor;
  
  /// Color of disabled dates
  final Color? disabledDateColor;
  
  /// Text color for disabled dates
  final Color? disabledDateTextColor;
  
  /// Color of event indicators
  final Color? eventIndicatorColor;
  
  // Interaction properties
  /// Callback when a date is selected
  final Function(DateTime)? onDateSelected;
  
  /// Callback when multiple dates are selected
  final Function(List<DateTime>)? onMultipleDatesSelected;
  
  /// Callback when month changes
  final Function(DateTime)? onMonthChanged;
  
  /// Callback when an event is tapped
  final Function(CalendarEvent)? onEventTapped;
  
  // Layout properties
  /// Width of the calendar
  final double? width;
  
  /// Height of the calendar
  final double? height;
  
  /// Padding around the calendar
  final EdgeInsets? padding;
  
  /// Margin around the calendar
  final EdgeInsets? margin;

  const CalendarWidget({
    super.key,
    this.size = CalendarSize.medium,
    this.initialSelectedDate,
    this.initialDisplayDate,
    this.allowDateSelection = true,
    this.allowMultipleSelection = false,
    this.initialSelectedDates,
    this.minDate,
    this.maxDate,
    this.events,
    this.showHeader = true,
    this.showNavigation = true,
    this.showWeekdayLabels = true,
    this.firstDayOfWeek = 1, // Monday
    this.locale = 'en_US',
    this.headingFontSize,
    this.bodyFontSize,
    this.labelFontSize,
    this.headingFontWeight,
    this.bodyFontWeight,
    this.labelFontWeight,
    this.borderWidth,
    this.borderColor,
    this.borderRadius,
    this.hasBorder = true,
    this.boxShadow,
    this.hasShadow = false,
    this.backgroundColor,
    this.headerColor,
    this.headerTextColor,
    this.selectedDateColor,
    this.selectedDateTextColor,
    this.todayColor,
    this.todayTextColor,
    this.weekdayLabelColor,
    this.dateColor,
    this.dateTextColor,
    this.disabledDateColor,
    this.disabledDateTextColor,
    this.eventIndicatorColor,
    this.onDateSelected,
    this.onMultipleDatesSelected,
    this.onMonthChanged,
    this.onEventTapped,
    this.width,
    this.height,
    this.padding,
    this.margin,
  });

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();

  // JsonSerializableWidget implementation
  @override
  String get widgetType => 'calendar';

  @override
  String get description => 'A customizable calendar widget with size variants and event support';

  @override
  bool validate({List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    if (minDate != null && maxDate != null && minDate!.isAfter(maxDate!)) {
      errors?.add('minDate cannot be after maxDate');
      isValid = false;
    }

    if (firstDayOfWeek < 0 || firstDayOfWeek > 6) {
      errors?.add('firstDayOfWeek must be between 0 and 6');
      isValid = false;
    }

    return isValid;
  }

  @override
  Map<String, dynamic> toJson() {
    // Convert events map to JSON
    Map<String, dynamic>? eventsJson;
    if (events != null) {
      eventsJson = {};
      events!.forEach((date, eventList) {
        final dateKey = date.toIso8601String().split('T')[0]; // YYYY-MM-DD format
        eventsJson![dateKey] = eventList.map((event) => event.toJson()).toList();
      });
    }

    return {
      'widgetType': widgetType,
      'schemaVersion': schemaVersion,
      'size': size.name,
      'initialSelectedDate': initialSelectedDate?.toIso8601String(),
      'initialDisplayDate': initialDisplayDate?.toIso8601String(),
      'allowDateSelection': allowDateSelection,
      'allowMultipleSelection': allowMultipleSelection,
      'initialSelectedDates': initialSelectedDates?.map((date) => date.toIso8601String()).toList(),
      'minDate': minDate?.toIso8601String(),
      'maxDate': maxDate?.toIso8601String(),
      'events': eventsJson,
      'showHeader': showHeader,
      'showNavigation': showNavigation,
      'showWeekdayLabels': showWeekdayLabels,
      'firstDayOfWeek': firstDayOfWeek,
      'locale': locale,
      'headingFontSize': headingFontSize,
      'bodyFontSize': bodyFontSize,
      'labelFontSize': labelFontSize,
      'headingFontWeight': headingFontWeight?.index,
      'bodyFontWeight': bodyFontWeight?.index,
      'labelFontWeight': labelFontWeight?.index,
      'borderWidth': borderWidth,
      'borderColor': ColorUtils.colorToJson(borderColor),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'boxShadow': boxShadow?.map((shadow) => _boxShadowToJson(shadow)).toList(),
      'hasShadow': hasShadow,
      'backgroundColor': ColorUtils.colorToJson(backgroundColor),
      'headerColor': ColorUtils.colorToJson(headerColor),
      'headerTextColor': ColorUtils.colorToJson(headerTextColor),
      'selectedDateColor': ColorUtils.colorToJson(selectedDateColor),
      'selectedDateTextColor': ColorUtils.colorToJson(selectedDateTextColor),
      'todayColor': ColorUtils.colorToJson(todayColor),
      'todayTextColor': ColorUtils.colorToJson(todayTextColor),
      'weekdayLabelColor': ColorUtils.colorToJson(weekdayLabelColor),
      'dateColor': ColorUtils.colorToJson(dateColor),
      'dateTextColor': ColorUtils.colorToJson(dateTextColor),
      'disabledDateColor': ColorUtils.colorToJson(disabledDateColor),
      'disabledDateTextColor': ColorUtils.colorToJson(disabledDateTextColor),
      'eventIndicatorColor': ColorUtils.colorToJson(eventIndicatorColor),
      'width': width,
      'height': height,
      'padding': padding != null ? _edgeInsetsToJson(padding!) : null,
      'margin': margin != null ? _edgeInsetsToJson(margin!) : null,
    };
  }

  /// Converts BoxShadow to JSON
  Map<String, dynamic> _boxShadowToJson(BoxShadow shadow) {
    return {
      'color': ColorUtils.colorToJson(shadow.color),
      'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
      'blurRadius': shadow.blurRadius,
      'spreadRadius': shadow.spreadRadius,
    };
  }

  /// Converts EdgeInsets to JSON
  Map<String, dynamic> _edgeInsetsToJson(EdgeInsets insets) {
    return {
      'left': insets.left,
      'top': insets.top,
      'right': insets.right,
      'bottom': insets.bottom,
    };
  }

  @override
  Map<String, dynamic> getDefaultConfig() {
    return CalendarWidget().toJson();
  }

  @override
  JsonSerializableWidget copyWith(Map<String, dynamic> changes) {
    final currentJson = toJson();
    final mergedJson = {...currentJson, ...changes};
    return CalendarWidget.fromJson(mergedJson);
  }

  /// Creates a CalendarWidget from JSON
  factory CalendarWidget.fromJson(Map<String, dynamic> json) {
    // Parse events from JSON
    Map<DateTime, List<CalendarEvent>>? events;
    if (json['events'] != null) {
      events = {};
      final eventsJson = json['events'] as Map<String, dynamic>;
      eventsJson.forEach((dateKey, eventListJson) {
        final date = DateTime.parse(dateKey);
        final eventList = (eventListJson as List)
            .map((eventJson) => CalendarEvent.fromJson(eventJson as Map<String, dynamic>))
            .toList();
        events![date] = eventList;
      });
    }

    return CalendarWidget(
      size: CalendarSize.values.firstWhere(
        (e) => e.name == json['size'],
        orElse: () => CalendarSize.medium,
      ),
      initialSelectedDate: json['initialSelectedDate'] != null
          ? DateTime.parse(json['initialSelectedDate'])
          : null,
      initialDisplayDate: json['initialDisplayDate'] != null
          ? DateTime.parse(json['initialDisplayDate'])
          : null,
      allowDateSelection: json['allowDateSelection'] ?? true,
      allowMultipleSelection: json['allowMultipleSelection'] ?? false,
      initialSelectedDates: json['initialSelectedDates'] != null
          ? (json['initialSelectedDates'] as List)
              .map((dateStr) => DateTime.parse(dateStr))
              .toList()
          : null,
      minDate: json['minDate'] != null ? DateTime.parse(json['minDate']) : null,
      maxDate: json['maxDate'] != null ? DateTime.parse(json['maxDate']) : null,
      events: events,
      showHeader: json['showHeader'] ?? true,
      showNavigation: json['showNavigation'] ?? true,
      showWeekdayLabels: json['showWeekdayLabels'] ?? true,
      firstDayOfWeek: json['firstDayOfWeek'] ?? 1,
      locale: json['locale'] ?? 'en_US',
      headingFontSize: json['headingFontSize']?.toDouble(),
      bodyFontSize: json['bodyFontSize']?.toDouble(),
      labelFontSize: json['labelFontSize']?.toDouble(),
      headingFontWeight: json['headingFontWeight'] != null
          ? FontWeight.values[json['headingFontWeight']]
          : null,
      bodyFontWeight: json['bodyFontWeight'] != null
          ? FontWeight.values[json['bodyFontWeight']]
          : null,
      labelFontWeight: json['labelFontWeight'] != null
          ? FontWeight.values[json['labelFontWeight']]
          : null,
      borderWidth: json['borderWidth']?.toDouble(),
      borderColor: ColorUtils.jsonToColor(json['borderColor']),
      borderRadius: json['borderRadius']?.toDouble(),
      hasBorder: json['hasBorder'] ?? true,
      boxShadow: json['boxShadow'] != null
          ? (json['boxShadow'] as List)
              .map((shadowJson) => _boxShadowFromJson(shadowJson as Map<String, dynamic>))
              .toList()
          : null,
      hasShadow: json['hasShadow'] ?? false,
      backgroundColor: ColorUtils.jsonToColor(json['backgroundColor']),
      headerColor: ColorUtils.jsonToColor(json['headerColor']),
      headerTextColor: ColorUtils.jsonToColor(json['headerTextColor']),
      selectedDateColor: ColorUtils.jsonToColor(json['selectedDateColor']),
      selectedDateTextColor: ColorUtils.jsonToColor(json['selectedDateTextColor']),
      todayColor: ColorUtils.jsonToColor(json['todayColor']),
      todayTextColor: ColorUtils.jsonToColor(json['todayTextColor']),
      weekdayLabelColor: ColorUtils.jsonToColor(json['weekdayLabelColor']),
      dateColor: ColorUtils.jsonToColor(json['dateColor']),
      dateTextColor: ColorUtils.jsonToColor(json['dateTextColor']),
      disabledDateColor: ColorUtils.jsonToColor(json['disabledDateColor']),
      disabledDateTextColor: ColorUtils.jsonToColor(json['disabledDateTextColor']),
      eventIndicatorColor: ColorUtils.jsonToColor(json['eventIndicatorColor']),
      width: json['width']?.toDouble(),
      height: json['height']?.toDouble(),
      padding: json['padding'] != null ? _edgeInsetsFromJson(json['padding']) : null,
      margin: json['margin'] != null ? _edgeInsetsFromJson(json['margin']) : null,
    );
  }

  /// Converts JSON to BoxShadow
  static BoxShadow _boxShadowFromJson(Map<String, dynamic> json) {
    return BoxShadow(
      color: ColorUtils.jsonToColor(json['color']) ?? Colors.black,
      offset: Offset(
        json['offset']['dx']?.toDouble() ?? 0.0,
        json['offset']['dy']?.toDouble() ?? 0.0,
      ),
      blurRadius: json['blurRadius']?.toDouble() ?? 0.0,
      spreadRadius: json['spreadRadius']?.toDouble() ?? 0.0,
    );
  }

  /// Converts JSON to EdgeInsets
  static EdgeInsets _edgeInsetsFromJson(Map<String, dynamic> json) {
    return EdgeInsets.only(
      left: json['left']?.toDouble() ?? 0.0,
      top: json['top']?.toDouble() ?? 0.0,
      right: json['right']?.toDouble() ?? 0.0,
      bottom: json['bottom']?.toDouble() ?? 0.0,
    );
  }

  /// Creates a CalendarWidget from JSON string
  factory CalendarWidget.fromJsonString(String jsonString) {
    final json = Map<String, dynamic>.from(
      jsonDecode(jsonString) as Map
    );
    return CalendarWidget.fromJson(json);
  }

  /// Converts the widget to JSON string
  String toJsonString({bool prettyPrint = false}) {
    final json = toJson();
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }

  // Missing JsonSerializableWidget interface methods
  @override
  String get schemaVersion => '1.0.0';

  @override
  Map<String, dynamic> getDelta() {
    final current = toJson();
    final defaults = getDefaultConfig();
    final delta = <String, dynamic>{};

    current.forEach((key, value) {
      if (!defaults.containsKey(key) || defaults[key] != value) {
        delta[key] = value;
      }
    });

    return delta;
  }

  @override
  Map<String, dynamic> mergeConfig(Map<String, dynamic> other) {
    final thisConfig = toJson();
    final merged = Map<String, dynamic>.from(thisConfig);

    other.forEach((key, value) {
      if (value != null) {
        merged[key] = value;
      }
    });

    return merged;
  }
}

/// State class for CalendarWidget
class _CalendarWidgetState extends State<CalendarWidget> {
  late DateTime _displayDate;
  DateTime? _selectedDate;
  List<DateTime> _selectedDates = [];

  @override
  void initState() {
    super.initState();
    _displayDate = widget.initialDisplayDate ?? DateTime.now();
    _selectedDate = widget.initialSelectedDate;
    _selectedDates = widget.initialSelectedDates ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? _getDefaultWidth(context),
      height: widget.height ?? _getDefaultHeight(context),
      margin: widget.margin,
      padding: widget.padding ?? _getDefaultPadding(),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.white,
        border: widget.hasBorder
            ? Border.all(
                color: widget.borderColor ?? Colors.grey.shade300,
                width: widget.borderWidth ?? _getDefaultBorderWidth(),
              )
            : null,
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? _getDefaultBorderRadius()
        ),
        boxShadow: widget.hasShadow
            ? widget.boxShadow ?? _getDefaultBoxShadow()
            : null,
      ),
      child: Column(
        children: [
          _buildCalendarHeader(),
          _buildNavigationRow(),
          _buildWeekdayLabels(),
          Expanded(child: _buildCalendarGrid()),
        ],
      ),
    );
  }

  // Build the top header with "Calendar" title and month dropdown
  Widget _buildCalendarHeader() {
    return Container(
      // padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
         color: widget.headerColor ?? Colors.white,
        // border: widget.hasBorder
        //     ? Border(
        //         bottom: BorderSide(
        //           color: widget.borderColor ?? Colors.grey.shade200,
        //           width: 1.0,
        //         ),
        //       )
        //     : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Calendar title
          Text(
            'Calendar',
            style: TextStyle(
              fontSize: widget.headingFontSize ?? _getDefaultHeadingFontSize(),
              fontWeight: widget.headingFontWeight ?? FontWeight.w500,
              color: widget.headerTextColor ??const Color(0xFF434447),
              fontFamily: 'TimepesText',
            ),
          ),

          // Month dropdown
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Month',
                style: TextStyle(
                  fontSize: widget.bodyFontSize ?? _getDefaultBodyFontSize(),
                  color: widget.headerTextColor ?? const Color(0xFFA5A5A5),
                  fontFamily: 'TimepesText',
                  fontWeight: widget.headingFontWeight ?? FontWeight.w500,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.arrow_drop_down,
                color: widget.headerTextColor ?? const Color(0xFFA5A5A5),
                size: widget.bodyFontSize ?? _getDefaultBodyFontSize(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build the month navigation row with arrows and month name
  Widget _buildNavigationRow() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Left arrow
          IconButton(
            onPressed: _previousMonth,
            icon: const Icon(Icons.chevron_left),
            color: widget.headerTextColor ??const Color(0xFF707070),
            iconSize: 18,
          ),

          // Month and year
          Text(
            _formatHeaderDate(),
            style: TextStyle(
              fontSize: widget.bodyFontSize ?? _getDefaultCenterFontSize(),
              fontWeight: widget.bodyFontWeight ?? FontWeight.w600,
              color: widget.headerTextColor ??const Color(0xFF434447),
              fontFamily: 'Inter',
            ),
          ),

          // Right arrow
          IconButton(
            onPressed: _nextMonth,
            icon: const Icon(Icons.chevron_right),
            color: widget.headerTextColor ?? const Color(0xFF707070),
            iconSize: 18,
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdayLabels() {
    final weekdays =  
    widget.size == CalendarSize.large
        ? _getWeekdayLabelsLarge()
        : widget.size == CalendarSize.medium
            ? _getWeekdayLabelsMedium()
            : _getWeekdayLabelsSmall();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8,horizontal:8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: widget.borderColor ?? Colors.grey.shade200,
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: weekdays.map((weekday) =>
          Expanded(
            child: Center(
              child: Text(
                weekday,
                style: TextStyle(
                  fontSize: widget.labelFontSize ?? _getDefaultLabelFontSize(),
                  fontWeight: widget.labelFontWeight ?? FontWeight.w500,
                  color: widget.weekdayLabelColor ?? const Color(0xFF323232),
                  fontFamily: 'Inter',
                ),
              ),
            ),
          ),
        ).toList(),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final daysInMonth = _getDaysInMonth();
    final firstDayOfMonth = DateTime(_displayDate.year, _displayDate.month, 1);
    final startingWeekday = (firstDayOfMonth.weekday - widget.firstDayOfWeek) % 7;

    // Build calendar as a table-like structure
    List<Widget> weeks = [];

    for (int week = 0; week < 6; week++) {
      List<Widget> days = [];

      for (int day = 0; day < 7; day++) {
        final index = week * 7 + day;
        final dayIndex = index - startingWeekday;

        if (dayIndex < 0 || dayIndex >= daysInMonth) {
          days.add(_buildEmptyDateCell()); // Empty cell for previous/next month
        } else {
          final date = DateTime(_displayDate.year, _displayDate.month, dayIndex + 1);
          days.add(_buildDateCell(date));
        }
      }

      weeks.add(
        Row(
          children: days.map((day) => Expanded(child: day)).toList(),
        ),
      );
    }

    return Column(
      children: weeks.map((week) => Expanded(child: week)).toList(),
    );
  }

  Widget _buildEmptyDateCell() {
    return Container(
      height: _getDefaultCellHeight(),
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.borderColor ?? Colors.grey.shade200,
          width: 0.5,
        ),
      ),
    );
  }

  Widget _buildDateCell(DateTime date) {
    final isSelected = _isDateSelected(date);
    final isToday = _isToday(date);
    final isDisabled = _isDateDisabled(date);
    final events = widget.events?[date] ?? [];

    return GestureDetector(
      onTap: isDisabled ? null : () => _selectDate(date),
      onLongPress: () => _showEventDialog(date), // Long press to add events
      child: Container(
        height: _getDefaultCellHeight(),
        decoration: BoxDecoration(
          color: isSelected
              ? widget.selectedDateColor ?? Colors.blue
              : isToday
                  ? widget.todayColor ?? Colors.orange.shade100
                  : widget.dateColor ?? Colors.white,
          border: Border.all(
            color: widget.borderColor ?? Colors.grey.shade200,
            width: 0.5,
          ),
        ),
        child:
         Padding(
          padding: widget.size == CalendarSize.small
              ? const EdgeInsets.all(0)
              : widget.size == CalendarSize.medium
                  ? const EdgeInsets.all(0)
                  : const EdgeInsets.all(4),
          child:
           Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date number
             (widget.size == CalendarSize.small || widget.size == CalendarSize.medium)
    ? Center(
        child: Text(
          date.day.toString().padLeft(2, '0'),
          style: TextStyle(
            fontSize: widget.bodyFontSize ?? _getDefaultLabelFontSize(),
            fontWeight: widget.bodyFontWeight ?? FontWeight.w500,
            fontFamily: 'Inter',
            color: isSelected
                ? widget.selectedDateTextColor ?? Colors.white
                : isToday
                    ? widget.todayTextColor ?? Colors.orange.shade800
                    : isDisabled
                        ? widget.disabledDateTextColor ?? Colors.grey
                        : widget.dateTextColor ?? const Color(0xFF323232),
          ),
        ),
      )
    : Text(
        date.day.toString().padLeft(2, '0'),
        style: TextStyle(
          fontSize: widget.bodyFontSize ?? _getDefaultLabelFontSize(),
          fontWeight: widget.bodyFontWeight ?? FontWeight.w500,
          fontFamily: 'Inter',
          color: isSelected
              ? widget.selectedDateTextColor ?? Colors.white
              : isToday
                  ? widget.todayTextColor ?? Colors.orange.shade800
                  : isDisabled
                      ? widget.disabledDateTextColor ?? Colors.grey
                      : widget.dateTextColor ?? const Color(0xFF323232),
        ),
      ),


              // Events display
              if (events.isNotEmpty) ...[
                const SizedBox(height: 2),
                ...events.take(2).map((event) => _buildEventIndicator(event)),
                if (events.length > 2)
                  Text(
                    '+${events.length - 2} more',
                    style: TextStyle(
                      fontSize: 8,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ],
          ),
        ),),
      );
    // );
  }

  Widget _buildEventIndicator(CalendarEvent event) {
    return Container(
      width: double.infinity,
      height: 12,
      margin: const EdgeInsets.only(bottom: 1),
      padding: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: event.color ?? widget.eventIndicatorColor ?? Colors.blue,
        borderRadius: BorderRadius.circular(2),
      ),
      child: Text(
        event.title,
        style: const TextStyle(
          fontSize: 8,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  // Helper methods for default values based on size
 double _getDefaultWidth(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;

  switch (widget.size) {
    case CalendarSize.small:
      return screenWidth * (192 / 1366);  // ~14%
    case CalendarSize.medium:
      return screenWidth * (400 / 1366);  // ~29%
    case CalendarSize.large:
      return screenWidth * (848 / 1366);  // ~62%
  }
}

double _getDefaultHeight(BuildContext context) {
  double screenHeight = MediaQuery.of(context).size.height;

  switch (widget.size) {
    case CalendarSize.small:
      return screenHeight * (251 / 768);  // Assuming 1366x768 screen
    case CalendarSize.medium:
      return screenHeight * (361 / 768);
    case CalendarSize.large:
      return screenHeight * (445 / 768);
  }
}


  EdgeInsets _getDefaultPadding() {
    switch (widget.size) {
      case CalendarSize.small:
        return const EdgeInsets.all(8);
      case CalendarSize.medium:
        return const EdgeInsets.all(12);
      case CalendarSize.large:
        return const EdgeInsets.all(16);
    }
  }

  double _getDefaultBorderWidth() {
    switch (widget.size) {
      case CalendarSize.small:
        return 0.5;
      case CalendarSize.medium:
        return 1.0;
      case CalendarSize.large:
        return 2.0;
    }
  }

  double _getDefaultBorderRadius() {
    switch (widget.size) {
      case CalendarSize.small:
        return 6.0;
      case CalendarSize.medium:
        return 16.0;
      case CalendarSize.large:
        return 24.0;
    }
  }

  List<BoxShadow> _getDefaultBoxShadow() {
    switch (widget.size) {
      case CalendarSize.small:
        return [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ];
      case CalendarSize.medium:
        return [
          BoxShadow(
            color: Colors.black.withOpacity(0.12),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ];
      case CalendarSize.large:
        return [
          BoxShadow(
            color: Colors.black.withOpacity(0.16),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ];
    }
  }

  double _getDefaultHeadingFontSize() {
    switch (widget.size) {
      case CalendarSize.small:
        return 14.0;
      case CalendarSize.medium:
        return 14.0;
      case CalendarSize.large:
        return 16.0;
    }
  }

  double _getDefaultBodyFontSize() {
    switch (widget.size) {
      case CalendarSize.small:
        return 12.0;
      case CalendarSize.medium:
        return 12.0;
      case CalendarSize.large:
        return 14.0;
    }
  }
    double _getDefaultCenterFontSize() {
    switch (widget.size) {
      case CalendarSize.small:
        return 12.0;
      case CalendarSize.medium:
        return 14.0;
      case CalendarSize.large:
        return 16.0;
    }
  }

  double _getDefaultLabelFontSize() {
    switch (widget.size) {
      case CalendarSize.small:
        return 8.0;
      case CalendarSize.medium:
        return 12.0;
      case CalendarSize.large:
        return 14.0;
    }
  }

  double _getDefaultCellHeight() {
    double screenHeight = MediaQuery.of(context).size.height;

  switch (widget.size) {
    case CalendarSize.small:
      return screenHeight * (29 / 768);  // Assuming 1366x768 screen
    case CalendarSize.medium:
      return screenHeight * (50 / 768);
    case CalendarSize.large:
      return screenHeight * (63 / 768);
  }
    // switch (widget.size) {
    //   case CalendarSize.small:
    //     return 29.0;
    //   case CalendarSize.medium:
    //     return 40.0;
    //   case CalendarSize.large:
    //     return 48.0;
    // }
  }

  // Show event dialog for adding/editing events
  void _showEventDialog(DateTime date) {
    // This will be implemented later for event scheduling
    // For now, just show a simple dialog
    if (widget.onDateSelected != null) {
      widget.onDateSelected!(date);
    }
  }

  // Calendar logic methods
  void _previousMonth() {
    setState(() {
      _displayDate = DateTime(_displayDate.year, _displayDate.month - 1);
    });
    widget.onMonthChanged?.call(_displayDate);
  }

  void _nextMonth() {
    setState(() {
      _displayDate = DateTime(_displayDate.year, _displayDate.month + 1);
    });
    widget.onMonthChanged?.call(_displayDate);
  }

  void _selectDate(DateTime date) {
    if (!widget.allowDateSelection) return;

    setState(() {
      if (widget.allowMultipleSelection) {
        if (_selectedDates.contains(date)) {
          _selectedDates.remove(date);
        } else {
          _selectedDates.add(date);
        }
        widget.onMultipleDatesSelected?.call(_selectedDates);
      } else {
        _selectedDate = date;
        widget.onDateSelected?.call(date);
      }
    });
  }

  String _formatHeaderDate() {
    return '${_getMonthName(_displayDate.month)} ${_displayDate.year}';
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  List<String> _getWeekdayLabelsLarge() {
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Satarday'];
    final reorderedWeekdays = <String>[];

    for (int i = 0; i < 7; i++) {
      final index = (widget.firstDayOfWeek + i) % 7;
      reorderedWeekdays.add(weekdays[index]);
    }

    return reorderedWeekdays;
  }
   List<String> _getWeekdayLabelsMedium() {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    final reorderedWeekdays = <String>[];

    for (int i = 0; i < 7; i++) {
      final index = (widget.firstDayOfWeek + i) % 7;
      reorderedWeekdays.add(weekdays[index]);
    }

    return reorderedWeekdays;
  }
   List<String> _getWeekdayLabelsSmall() {
    const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    final reorderedWeekdays = <String>[];

    for (int i = 0; i < 7; i++) {
      final index = (widget.firstDayOfWeek + i) % 7;
      reorderedWeekdays.add(weekdays[index]);
    }

    return reorderedWeekdays;
  }

  int _getDaysInMonth() {
    return DateTime(_displayDate.year, _displayDate.month + 1, 0).day;
  }

  bool _isDateSelected(DateTime date) {
    if (widget.allowMultipleSelection) {
      return _selectedDates.any((selected) => _isSameDay(selected, date));
    }
    return _selectedDate != null && _isSameDay(_selectedDate!, date);
  }

  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return _isSameDay(date, today);
  }

  bool _isDateDisabled(DateTime date) {
    if (widget.minDate != null && date.isBefore(widget.minDate!)) {
      return true;
    }
    if (widget.maxDate != null && date.isAfter(widget.maxDate!)) {
      return true;
    }
    return false;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}
