import 'dart:convert';

/// Utility class for JSON operations
class JsonUtils {
  /// Pretty prints JSON with indentation
  static String prettyPrint(Map<String, dynamic> json) {
    const JsonEncoder encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(json);
  }

  /// Safely gets a value from JSON with type checking
  static T? safeGet<T>(Map<String, dynamic> json, String key, {T? defaultValue}) {
    if (!json.containsKey(key)) return defaultValue;
    
    final value = json[key];
    if (value is T) return value;
    
    return defaultValue;
  }

  /// Safely gets a string value from JSON
  static String? getString(Map<String, dynamic> json, String key, {String? defaultValue}) {
    return safeGet<String>(json, key, defaultValue: defaultValue);
  }

  /// Safely gets an integer value from JSON
  static int? getInt(Map<String, dynamic> json, String key, {int? defaultValue}) {
    final value = json[key];
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return defaultValue;
  }

  /// Safely gets a double value from JSON
  static double? getDouble(Map<String, dynamic> json, String key, {double? defaultValue}) {
    final value = json[key];
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return defaultValue;
  }

  /// Safely gets a boolean value from JSON
  static bool? getBool(Map<String, dynamic> json, String key, {bool? defaultValue}) {
    final value = json[key];
    if (value is bool) return value;
    if (value is String) {
      final lower = value.toLowerCase();
      if (lower == 'true') return true;
      if (lower == 'false') return false;
    }
    return defaultValue;
  }

  /// Safely gets a DateTime value from JSON
  static DateTime? getDateTime(Map<String, dynamic> json, String key, {DateTime? defaultValue}) {
    final value = json[key];
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// Safely gets a List value from JSON
  static List<T>? getList<T>(Map<String, dynamic> json, String key, {List<T>? defaultValue}) {
    final value = json[key];
    if (value is List) {
      try {
        return value.cast<T>();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// Safely gets a Map value from JSON
  static Map<String, dynamic>? getMap(Map<String, dynamic> json, String key, {Map<String, dynamic>? defaultValue}) {
    return safeGet<Map<String, dynamic>>(json, key, defaultValue: defaultValue);
  }

  /// Validates that JSON contains required fields
  static bool hasRequiredFields(Map<String, dynamic> json, List<String> requiredFields, {List<String>? missingFields}) {
    missingFields?.clear();
    bool hasAll = true;
    
    for (final field in requiredFields) {
      if (!json.containsKey(field) || json[field] == null) {
        missingFields?.add(field);
        hasAll = false;
      }
    }
    
    return hasAll;
  }

  /// Removes null values from JSON
  static Map<String, dynamic> removeNulls(Map<String, dynamic> json) {
    final cleaned = <String, dynamic>{};
    
    json.forEach((key, value) {
      if (value != null) {
        if (value is Map<String, dynamic>) {
          final cleanedMap = removeNulls(value);
          if (cleanedMap.isNotEmpty) {
            cleaned[key] = cleanedMap;
          }
        } else if (value is List) {
          final cleanedList = value.where((item) => item != null).toList();
          if (cleanedList.isNotEmpty) {
            cleaned[key] = cleanedList;
          }
        } else {
          cleaned[key] = value;
        }
      }
    });
    
    return cleaned;
  }

  /// Deep merges two JSON objects
  static Map<String, dynamic> deepMerge(Map<String, dynamic> base, Map<String, dynamic> override) {
    final result = Map<String, dynamic>.from(base);
    
    override.forEach((key, value) {
      if (value is Map<String, dynamic> && result[key] is Map<String, dynamic>) {
        result[key] = deepMerge(result[key] as Map<String, dynamic>, value);
      } else {
        result[key] = value;
      }
    });
    
    return result;
  }

  /// Flattens a nested JSON object
  static Map<String, dynamic> flatten(Map<String, dynamic> json, {String separator = '.'}) {
    final flattened = <String, dynamic>{};
    
    void _flatten(Map<String, dynamic> obj, String prefix) {
      obj.forEach((key, value) {
        final newKey = prefix.isEmpty ? key : '$prefix$separator$key';
        
        if (value is Map<String, dynamic>) {
          _flatten(value, newKey);
        } else {
          flattened[newKey] = value;
        }
      });
    }
    
    _flatten(json, '');
    return flattened;
  }

  /// Unflattens a flattened JSON object
  static Map<String, dynamic> unflatten(Map<String, dynamic> flattened, {String separator = '.'}) {
    final result = <String, dynamic>{};
    
    flattened.forEach((key, value) {
      final parts = key.split(separator);
      Map<String, dynamic> current = result;
      
      for (int i = 0; i < parts.length - 1; i++) {
        final part = parts[i];
        current[part] ??= <String, dynamic>{};
        current = current[part] as Map<String, dynamic>;
      }
      
      current[parts.last] = value;
    });
    
    return result;
  }
}
