import 'package:dio/dio.dart';
import '../models/object_creation_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling object creation/extraction API operations
class ObjectCreationService extends BaseApiService {
  // Base URL for the extraction API
  static const String _baseUrl = 'http://10.26.1.11:8556';
  static const String _extractEndpoint = '/api/v1/extract';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  // Configuration constants
  static const int _maxRetries = 3;
  static const int _retryDelayMs = 1000; // Fixed 1 second delay

  /// Execute an operation with retry logic
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = _maxRetries,
  }) async {
    int attempt = 0;

    while (attempt <= maxRetries) {
      try {
        if (attempt > 0) {
          Logger.info(
              '${operationName ?? 'Operation'} retry attempt $attempt/$maxRetries');
        }
        return await operation();
      } catch (e) {
        attempt++;

        // Check if we should retry this error
        bool shouldRetry = _shouldRetryError(e);

        if (attempt > maxRetries || !shouldRetry) {
          Logger.error(
              '${operationName ?? 'Operation'} failed after $attempt attempts: $e');
          rethrow;
        }

        // Fixed delay instead of exponential backoff
        Logger.warning(
            '${operationName ?? 'Operation'} failed (attempt $attempt), retrying in ${_retryDelayMs}ms: $e');
        await Future.delayed(Duration(milliseconds: _retryDelayMs));
      }
    }
    throw Exception('This should never be reached');
  }

  /// Check if an error should be retried
  bool _shouldRetryError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.connectionError:
          return true;
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          return statusCode != null &&
              [408, 429, 500, 502, 503, 504].contains(statusCode);
        case DioExceptionType.cancel:
          return false; // Don't retry cancelled requests
        default:
          return false;
      }
    }
    return false;
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage(dynamic error) {
    if (error is DioException) {
      // Use the enhanced error message from DioClient if available
      if (error.message != null && !error.message!.contains('DioException')) {
        return error.message!;
      }

      // Fallback to basic error handling
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Request timeout. Please check your connection and try again.';
        case DioExceptionType.connectionError:
          return 'Unable to connect to the server. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          if (statusCode == 401) {
            return 'Authentication failed. Please log in again.';
          }
          if (statusCode == 403) {
            return 'You don\'t have permission to perform this action.';
          }
          if (statusCode == 404) {
            return 'The requested resource was not found.';
          }
          if (statusCode == 500) {
            return 'Server error. Please try again later.';
          }
          return 'An error occurred. Please try again.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }

  /// Basic API call method without retry (for internal use)
  Future<Response> _makeApiCall({
    required String method,
    required String url,
    Map<String, dynamic>? data,
    Options? options,
  }) async {
    switch (method.toUpperCase()) {
      case 'GET':
        return await dio.get(url, options: options);
      case 'POST':
        return await dio.post(url, data: data, options: options);
      case 'PUT':
        return await dio.put(url, data: data, options: options);
      case 'DELETE':
        return await dio.delete(url, options: options);
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }

  /// Get entities for a specific job with retry logic and proper error handling
  Future<ObjectCreationResponse> getJobEntities(String jobId) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Fetching entities for job: $jobId');

        final endpoint = '/api/v1/extraction/jobs/$jobId/entities';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );
        // var map = [
        //   {
        //     "name": "Contact",
        //     "displayName": "Contact",
        //     "type": "master",
        //     "description":
        //         "Core entity representing an individual person in the CRM system",
        //     "businessPurpose":
        //         "Primary record for managing relationships with individuals",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "core",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["master", "core"],
        //     "id": "7e740f15-a954-497a-84f3-5d166259b578",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Contact ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the contact",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "firstName",
        //         "displayName": "First Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Contact's first name",
        //         "helperText": "Enter firstName",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "lastName",
        //         "displayName": "Last Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Contact's last name",
        //         "helperText": "Enter lastName",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "primaryEmail",
        //         "displayName": "Primary Email",
        //         "dataType": "email",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary email address",
        //         "helperText": "Enter primaryEmail",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "secondaryEmail",
        //         "displayName": "Secondary Email",
        //         "dataType": "email",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Secondary email address",
        //         "helperText": "Enter secondaryEmail",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "primaryPhone",
        //         "displayName": "Primary Phone",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary phone number",
        //         "helperText": "Enter primaryPhone",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "mobilePhone",
        //         "displayName": "Mobile Phone",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Mobile phone number",
        //         "helperText": "Enter mobilePhone",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "jobTitle",
        //         "displayName": "Job Title",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Current job title",
        //         "helperText": "Enter jobTitle",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "department",
        //         "displayName": "Department",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Department within organization",
        //         "helperText": "Enter department",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "ACTIVE",
        //         "description": "Current status of the contact",
        //         "helperText": "Enter status",
        //         "enumValues": ["ACTIVE", "INACTIVE", "ARCHIVED"],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record creation timestamp",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Updated At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Last update timestamp",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Company",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "companyId",
        //         "description": "Primary company association"
        //       },
        //       {
        //         "relatedEntity": "Address",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "contactId",
        //         "description": "Contact addresses"
        //       },
        //       {
        //         "relatedEntity": "Communication",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "contactId",
        //         "description": "Communication history"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "primaryEmail",
        //         "operator": "MATCHES_REGEX",
        //         "value": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}",
        //         "errorMessage": "Invalid email format"
        //       },
        //       {
        //         "attributeName": "primaryPhone",
        //         "operator": "MATCHES_REGEX",
        //         "value": "^\\+?[1-9]\\d{1,14}",
        //         "errorMessage": "Invalid phone number format"
        //       },
        //       {
        //         "attributeName": "lastName",
        //         "operator": "IS_NOT_NULL",
        //         "errorMessage": "Last name is required"
        //       },
        //       {
        //         "attributeName": "status",
        //         "operator": "IN",
        //         "value": ["ACTIVE", "INACTIVE", "ARCHIVED"],
        //         "errorMessage": "Invalid status value"
        //       }
        //     ],
        //     "enum_values": [
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "ACTIVE",
        //         "display": "Active",
        //         "description": "ACTIVE option",
        //         "sortOrder": 1,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "INACTIVE",
        //         "display": "Inactive",
        //         "description": "INACTIVE option",
        //         "sortOrder": 2,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "ARCHIVED",
        //         "display": "Archived",
        //         "description": "ARCHIVED option",
        //         "sortOrder": 3,
        //         "active": true
        //       }
        //     ],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "Contact.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.firstName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.lastName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.primaryEmail",
        //         "classification": "confidential",
        //         "piiType": "email",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***@***.***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.secondaryEmail",
        //         "classification": "confidential",
        //         "piiType": "email",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***@***.***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.primaryPhone",
        //         "classification": "confidential",
        //         "piiType": "phone",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***-***-####",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.mobilePhone",
        //         "classification": "confidential",
        //         "piiType": "phone",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***-***-####",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.jobTitle",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.department",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.status",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Contact.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_CONTACT",
        //         "permissionName": "Contact Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "Contact",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to Contact entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage Contact records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_CONTACT",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage": "Admin has full access to Contact records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "Organization",
        //     "displayName": "Organization",
        //     "type": "master",
        //     "description":
        //         "Represents companies or entities that contacts are affiliated with",
        //     "businessPurpose":
        //         "Required for establishing professional relationships",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "core",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["master", "core"],
        //     "id": "5eb98faf-7298-4f4b-b581-8dd91758d811",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Organization ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the organization",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "name",
        //         "displayName": "Organization Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Legal name of the organization",
        //         "helperText": "Enter name",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "tradingName",
        //         "displayName": "Trading Name",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "DBA (Doing Business As) name if different from legal name",
        //         "helperText": "Enter tradingName",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "taxId",
        //         "displayName": "Tax ID",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": true,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Tax identification number",
        //         "helperText": "Enter taxId",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "industry",
        //         "displayName": "Industry",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary industry classification",
        //         "helperText": "Enter industry",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "website",
        //         "displayName": "Website",
        //         "dataType": "url",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Official website URL",
        //         "helperText": "Enter website",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "headquartersAddress",
        //         "displayName": "Headquarters Address",
        //         "dataType": "object",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary business address",
        //         "helperText": "Enter headquartersAddress",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "primaryPhone",
        //         "displayName": "Primary Phone",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Main contact number",
        //         "helperText": "Enter primaryPhone",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "primaryEmail",
        //         "displayName": "Primary Email",
        //         "dataType": "email",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Main contact email",
        //         "helperText": "Enter primaryEmail",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "employeeCount",
        //         "displayName": "Employee Count",
        //         "dataType": "integer",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Number of employees",
        //         "helperText": "Enter employeeCount",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "active",
        //         "description": "Current status of the organization",
        //         "helperText": "Enter status",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record creation timestamp",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Updated At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Last update timestamp",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Contact",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "organizationId",
        //         "description": "Contacts associated with this organization"
        //       },
        //       {
        //         "relatedEntity": "Address",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "organizationId",
        //         "description": "Additional addresses for this organization"
        //       },
        //       {
        //         "relatedEntity": "Document",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "organizationId",
        //         "description": "Documents related to this organization"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "name",
        //         "operator": "IS_NOT_NULL",
        //         "errorMessage": "Organization name is required"
        //       },
        //       {
        //         "attributeName": "name",
        //         "operator": "MIN_LENGTH",
        //         "value": 2,
        //         "errorMessage":
        //             "Organization name must be at least 2 characters"
        //       },
        //       {
        //         "attributeName": "primaryEmail",
        //         "operator": "EMAIL_FORMAT",
        //         "errorMessage": "Invalid email format"
        //       },
        //       {
        //         "attributeName": "website",
        //         "operator": "URL_FORMAT",
        //         "errorMessage": "Invalid website URL format"
        //       },
        //       {
        //         "attributeName": "employeeCount",
        //         "operator": "MIN_VALUE",
        //         "value": 1,
        //         "errorMessage": "Employee count must be greater than 0"
        //       }
        //     ],
        //     "enum_values": [],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "Organization.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.name",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.tradingName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.taxId",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.industry",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.website",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.headquartersAddress",
        //         "classification": "confidential",
        //         "piiType": "address",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.primaryPhone",
        //         "classification": "confidential",
        //         "piiType": "phone",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***-***-####",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.primaryEmail",
        //         "classification": "confidential",
        //         "piiType": "email",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***@***.***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.employeeCount",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.status",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "Organization.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_ORGANIZATION",
        //         "permissionName": "Organization Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "Organization",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to Organization entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage Organization records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_ORGANIZATION",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage":
        //             "Admin has full access to Organization records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "ContactMethod",
        //     "displayName": "Contactmethod",
        //     "type": "reference",
        //     "description":
        //         "Stores different ways to communicate with a contact",
        //     "businessPurpose":
        //         "Enables multiple communication channels per contact",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "supporting",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["reference"],
        //     "id": "6d6a03d7-5a7f-44da-a972-65bf123c7237",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Contact Method ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the contact method",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "contactId",
        //         "displayName": "Contact ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "foreign_key",
        //         "defaultValue": "",
        //         "description": "Reference to the parent Contact",
        //         "helperText": "Enter contactId",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "type",
        //         "displayName": "Method Type",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "Type of contact method (EMAIL, PHONE, MOBILE, SOCIAL_MEDIA, OTHER)",
        //         "helperText": "Enter type",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "value",
        //         "displayName": "Contact Value",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "The actual contact information (email address, phone number, etc.)",
        //         "helperText": "Enter value",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "isPrimary",
        //         "displayName": "Primary Method",
        //         "dataType": "boolean",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_default",
        //         "defaultValue": false,
        //         "description":
        //             "Indicates if this is the primary contact method",
        //         "helperText": "Enter isPrimary",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "label",
        //         "displayName": "Label",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "User-defined label (e.g., 'Work Email', 'Home Phone')",
        //         "helperText": "Enter label",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_default",
        //         "defaultValue": "ACTIVE",
        //         "description": "Current status of the contact method",
        //         "helperText": "Enter status",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "lastVerifiedAt",
        //         "displayName": "Last Verified",
        //         "dataType": "timestamp",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Timestamp of last verification",
        //         "helperText": "Enter lastVerifiedAt",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record creation timestamp",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Updated At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record last update timestamp",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Contact",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "contactId",
        //         "description": "Each contact method belongs to a single contact"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "value",
        //         "operator": "FORMAT_VALIDATION",
        //         "errorMessage": "Invalid format for contact method value"
        //       },
        //       {
        //         "attributeName": "type",
        //         "operator": "CONDITIONAL_VALIDATION",
        //         "rule":
        //             "Value format must match type (email format for EMAIL, phone format for PHONE/MOBILE)",
        //         "errorMessage":
        //             "Contact method value does not match specified type"
        //       },
        //       {
        //         "attributeName": "isPrimary",
        //         "operator": "UNIQUE_PER_GROUP",
        //         "rule": "Only one primary contact method per type per contact",
        //         "errorMessage":
        //             "Multiple primary contact methods of same type not allowed"
        //       },
        //       {
        //         "attributeName": "status",
        //         "operator": "STATE_TRANSITION",
        //         "rule":
        //             "UNVERIFIED can only transition to VERIFIED after verification process",
        //         "errorMessage": "Invalid status transition"
        //       }
        //     ],
        //     "enum_values": [],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "ContactMethod.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.contactId",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.type",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.value",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.isPrimary",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.label",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.status",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.lastVerifiedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactMethod.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_CONTACTMETHOD",
        //         "permissionName": "ContactMethod Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "ContactMethod",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to ContactMethod entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage ContactMethod records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_CONTACTMETHOD",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage":
        //             "Admin has full access to ContactMethod records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "JobTitle",
        //     "displayName": "Jobtitle",
        //     "type": "reference",
        //     "description": "Standardized list of professional positions",
        //     "businessPurpose": "Normalizes professional role information",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "reference",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["reference"],
        //     "id": "70608b8d-26b9-441f-b844-01a587e5ca9a",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "static_value",
        //         "defaultValue": "active",
        //         "description": "",
        //         "helperText": "Enter status",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Job Title ID",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the job title",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "titleName",
        //         "displayName": "Job Title Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Standard name of the job title",
        //         "helperText": "Enter titleName",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "titleCategory",
        //         "displayName": "Category",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "Category of the job title (e.g., Technical, Management, Operations)",
        //         "helperText": "Enter titleCategory",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "hierarchyLevel",
        //         "displayName": "Hierarchy Level",
        //         "dataType": "integer",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Organizational hierarchy level (1-10)",
        //         "helperText": "Enter hierarchyLevel",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "isActive",
        //         "displayName": "Active Status",
        //         "dataType": "boolean",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": true,
        //         "description": "Indicates if the job title is currently in use",
        //         "helperText": "Enter isActive",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "description",
        //         "displayName": "Description",
        //         "dataType": "text",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Detailed description of the job title",
        //         "helperText": "Enter description",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created Date",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_date",
        //         "defaultValue": "",
        //         "description": "Record creation timestamp",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Last Updated Date",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_date",
        //         "defaultValue": "",
        //         "description": "Record last update timestamp",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Contact",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "jobTitleId",
        //         "description":
        //             "Associates contacts with their current job title"
        //       },
        //       {
        //         "relatedEntity": "Organization",
        //         "relationshipType": "many-to-many",
        //         "foreignKey": "organizationJobTitleId",
        //         "description":
        //             "Links job titles to organizations where they are used"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "titleName",
        //         "operator": "IS_NOT_NULL",
        //         "errorMessage": "Job title name is required"
        //       },
        //       {
        //         "attributeName": "titleName",
        //         "operator": "UNIQUE",
        //         "errorMessage": "Job title name must be unique"
        //       },
        //       {
        //         "attributeName": "hierarchyLevel",
        //         "operator": "RANGE",
        //         "value": "1-10",
        //         "errorMessage": "Hierarchy level must be between 1 and 10"
        //       },
        //       {
        //         "attributeName": "titleCategory",
        //         "operator": "IN_LIST",
        //         "value": [
        //           "Technical",
        //           "Management",
        //           "Operations",
        //           "Sales",
        //           "Support"
        //         ],
        //         "errorMessage": "Invalid job title category"
        //       }
        //     ],
        //     "enum_values": [],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "JobTitle.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.titleName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.titleCategory",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.hierarchyLevel",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.isActive",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.description",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "JobTitle.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_JOBTITLE",
        //         "permissionName": "JobTitle Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "JobTitle",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to JobTitle entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage JobTitle records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_JOBTITLE",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage": "Admin has full access to JobTitle records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "ContactOrganizationRelationship",
        //     "displayName": "Contactorganizationrelationship",
        //     "type": "association",
        //     "description":
        //         "Maps the relationship between contacts and organizations",
        //     "businessPurpose":
        //         "Maintains historical and current organizational affiliations",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "supporting",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["association"],
        //     "id": "157fa2f6-756e-45e9-8483-833136aa117a",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Relationship ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description":
        //             "Unique identifier for the contact-organization relationship",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "contactId",
        //         "displayName": "Contact ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "reference",
        //         "defaultValue": "",
        //         "description": "Reference to the associated Contact",
        //         "helperText": "Enter contactId",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "organizationId",
        //         "displayName": "Organization ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "reference",
        //         "defaultValue": "",
        //         "description": "Reference to the associated Organization",
        //         "helperText": "Enter organizationId",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "jobTitle",
        //         "displayName": "Job Title",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "Current or historical job title at the organization",
        //         "helperText": "Enter jobTitle",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "startDate",
        //         "displayName": "Start Date",
        //         "dataType": "date",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Date when the relationship began",
        //         "helperText": "Enter startDate",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "endDate",
        //         "displayName": "End Date",
        //         "dataType": "date",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "Date when the relationship ended (if applicable)",
        //         "helperText": "Enter endDate",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "isPrimary",
        //         "displayName": "Primary Relationship",
        //         "dataType": "boolean",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_default",
        //         "defaultValue": false,
        //         "description":
        //             "Indicates if this is the contact's primary organization",
        //         "helperText": "Enter isPrimary",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "relationshipType",
        //         "displayName": "Relationship Type",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description":
        //             "Type of relationship between contact and organization",
        //         "helperText": "Enter relationshipType",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_default",
        //         "defaultValue": "ACTIVE",
        //         "description": "Current status of the relationship",
        //         "helperText": "Enter status",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Timestamp when the record was created",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Updated At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Timestamp when the record was last updated",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Contact",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "contactId",
        //         "description": "Relationship to the Contact entity"
        //       },
        //       {
        //         "relatedEntity": "Organization",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "organizationId",
        //         "description": "Relationship to the Organization entity"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "startDate",
        //         "operator": "LESS_THAN_OR_EQUAL",
        //         "value": "CURRENT_DATE",
        //         "errorMessage": "Start date cannot be in the future"
        //       },
        //       {
        //         "attributeName": "endDate",
        //         "operator": "GREATER_THAN",
        //         "value": "startDate",
        //         "errorMessage": "End date must be after start date"
        //       },
        //       {
        //         "attributeName": "isPrimary",
        //         "operator": "UNIQUE_IF_TRUE",
        //         "scope": ["contactId", "status"],
        //         "errorMessage":
        //             "Only one active relationship can be primary for a contact"
        //       },
        //       {
        //         "attributeName": "status",
        //         "operator": "REQUIRES",
        //         "value": "endDate",
        //         "condition": "status == 'TERMINATED'",
        //         "errorMessage": "End date is required when status is TERMINATED"
        //       }
        //     ],
        //     "enum_values": [],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.contactId",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute":
        //             "ContactOrganizationRelationship.organizationId",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.jobTitle",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.startDate",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.endDate",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.isPrimary",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute":
        //             "ContactOrganizationRelationship.relationshipType",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.status",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "ContactOrganizationRelationship.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_CONTACTORGANIZATIONRELATIONSHIP",
        //         "permissionName":
        //             "ContactOrganizationRelationship Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "ContactOrganizationRelationship",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description":
        //             "Full access to ContactOrganizationRelationship entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage":
        //             "Permission to manage ContactOrganizationRelationship records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_CONTACTORGANIZATIONRELATIONSHIP",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage":
        //             "Admin has full access to ContactOrganizationRelationship records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "User",
        //     "displayName": "User",
        //     "type": "system",
        //     "description":
        //         "System users who access and manage contact information",
        //     "businessPurpose": "Required for security and audit tracking",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "security",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "blue",
        //     "icon": "database",
        //     "tags": ["system"],
        //     "id": "c2783e1b-6177-4a1d-b229-ecab185b5407",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "User ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the user",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "firstName",
        //         "displayName": "First Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "User's first name",
        //         "helperText": "Enter firstName",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "lastName",
        //         "displayName": "Last Name",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "User's last name",
        //         "helperText": "Enter lastName",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "email",
        //         "displayName": "Email Address",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary email address",
        //         "helperText": "Enter email",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "phoneNumber",
        //         "displayName": "Phone Number",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Primary phone number",
        //         "helperText": "Enter phoneNumber",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "jobTitle",
        //         "displayName": "Job Title",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "",
        //         "description": "Current job title",
        //         "helperText": "Enter jobTitle",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "user_input",
        //         "defaultValue": "ACTIVE",
        //         "description": "Current user status",
        //         "helperText": "Enter status",
        //         "enumValues": ["ACTIVE", "INACTIVE", "SUSPENDED"],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "lastLoginAt",
        //         "displayName": "Last Login",
        //         "dataType": "timestamp",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Timestamp of last login",
        //         "helperText": "Enter lastLoginAt",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "createdAt",
        //         "displayName": "Created At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record creation timestamp",
        //         "helperText": "Enter createdAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updatedAt",
        //         "displayName": "Updated At",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Record last update timestamp",
        //         "helperText": "Enter updatedAt",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Company",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "company_id",
        //         "description": "User's associated company"
        //       },
        //       {
        //         "relatedEntity": "Role",
        //         "relationshipType": "many-to-many",
        //         "joinTable": "user_roles",
        //         "description": "User's system roles"
        //       },
        //       {
        //         "relatedEntity": "ContactMethod",
        //         "relationshipType": "one-to-many",
        //         "foreignKey": "user_id",
        //         "description": "Additional contact methods"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "email",
        //         "operator": "MATCHES_PATTERN",
        //         "value": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}",
        //         "errorMessage": "Invalid email format"
        //       },
        //       {
        //         "attributeName": "phoneNumber",
        //         "operator": "MATCHES_PATTERN",
        //         "value": "^\\+?[1-9]\\d{1,14}",
        //         "errorMessage": "Invalid phone number format"
        //       },
        //       {
        //         "attributeName": "status",
        //         "operator": "IN",
        //         "value": ["ACTIVE", "INACTIVE", "SUSPENDED"],
        //         "errorMessage": "Invalid status value"
        //       },
        //       {
        //         "attributeName": "firstName",
        //         "operator": "MIN_LENGTH",
        //         "value": 2,
        //         "errorMessage": "First name must be at least 2 characters"
        //       }
        //     ],
        //     "enum_values": [
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "ACTIVE",
        //         "display": "Active",
        //         "description": "ACTIVE option",
        //         "sortOrder": 1,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "INACTIVE",
        //         "display": "Inactive",
        //         "description": "INACTIVE option",
        //         "sortOrder": 2,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.status",
        //         "enumName": "Status Options",
        //         "value": "SUSPENDED",
        //         "display": "Suspended",
        //         "description": "SUSPENDED option",
        //         "sortOrder": 3,
        //         "active": true
        //       }
        //     ],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "User.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.firstName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.lastName",
        //         "classification": "confidential",
        //         "piiType": "name",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.email",
        //         "classification": "confidential",
        //         "piiType": "email",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***@***.***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.phoneNumber",
        //         "classification": "confidential",
        //         "piiType": "phone",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***-***-####",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.jobTitle",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.status",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.lastLoginAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.createdAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "User.updatedAt",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_USER",
        //         "permissionName": "User Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "User",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to User entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage User records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_USER",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage": "Admin has full access to User records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   },
        //   {
        //     "name": "AuditLog",
        //     "displayName": "Auditlog",
        //     "type": "system",
        //     "description": "Tracks changes to contact and related information",
        //     "businessPurpose": "Required for compliance and change tracking",
        //     "businessDomain": "CRM (Customer Relationship Management)",
        //     "category": "audit",
        //     "archivalStrategy": "archive_only",
        //     "colorTheme": "indigo",
        //     "icon": "database",
        //     "tags": ["system"],
        //     "id": "7dcbded9-5681-4470-8c21-63dbb182679d",
        //     "attributes": [
        //       {
        //         "name": "created_at",
        //         "displayName": "Created At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter created at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "updated_at",
        //         "displayName": "Updated At",
        //         "dataType": "datetime",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "current_timestamp",
        //         "defaultValue": "",
        //         "description": "",
        //         "helperText": "Enter updated at",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "status",
        //         "displayName": "Status",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "static_value",
        //         "defaultValue": "active",
        //         "description": "",
        //         "helperText": "Enter status",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "id",
        //         "displayName": "Audit Log ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": true,
        //         "defaultType": "auto_generated",
        //         "defaultValue": "",
        //         "description": "Unique identifier for the audit log entry",
        //         "helperText": "Enter id",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "entityType",
        //         "displayName": "Entity Type",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description":
        //             "Type of entity being audited (Contact, Company, etc.)",
        //         "helperText": "Enter entityType",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "entityId",
        //         "displayName": "Entity ID",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "ID of the entity being audited",
        //         "helperText": "Enter entityId",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "actionType",
        //         "displayName": "Action Type",
        //         "dataType": "enum",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Type of action performed",
        //         "helperText": "Enter actionType",
        //         "enumValues": ["CREATE", "UPDATE", "DELETE", "ACCESS"],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "changeDetails",
        //         "displayName": "Change Details",
        //         "dataType": "jsonb",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description":
        //             "JSON containing old and new values of changed fields",
        //         "helperText": "Enter changeDetails",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "performedBy",
        //         "displayName": "Performed By",
        //         "dataType": "uuid",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "ID of user who performed the action",
        //         "helperText": "Enter performedBy",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "ipAddress",
        //         "displayName": "IP Address",
        //         "dataType": "string",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "IP address where the action originated",
        //         "helperText": "Enter ipAddress",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       },
        //       {
        //         "name": "userAgent",
        //         "displayName": "User Agent",
        //         "dataType": "string",
        //         "required": false,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "Browser/client information",
        //         "helperText": "Enter userAgent",
        //         "enumValues": [],
        //         "validation": {"required": false}
        //       },
        //       {
        //         "name": "timestamp",
        //         "displayName": "Timestamp",
        //         "dataType": "timestamp",
        //         "required": true,
        //         "unique": false,
        //         "defaultType": "system_generated",
        //         "defaultValue": "",
        //         "description": "When the action occurred",
        //         "helperText": "Enter timestamp",
        //         "enumValues": [],
        //         "validation": {"required": true}
        //       }
        //     ],
        //     "relationships": [
        //       {
        //         "relatedEntity": "Contact",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "entityId",
        //         "description": "Related contact when entityType is Contact"
        //       },
        //       {
        //         "relatedEntity": "User",
        //         "relationshipType": "many-to-one",
        //         "foreignKey": "performedBy",
        //         "description": "User who performed the action"
        //       }
        //     ],
        //     "business_rules": [
        //       {
        //         "attributeName": "entityType",
        //         "operator": "IN",
        //         "value": ["Contact", "Company", "User"],
        //         "errorMessage": "Invalid entity type"
        //       },
        //       {
        //         "attributeName": "changeDetails",
        //         "operator": "IS_VALID_JSON",
        //         "errorMessage": "Change details must be valid JSON"
        //       },
        //       {
        //         "attributeName": "ipAddress",
        //         "operator": "MATCHES_PATTERN",
        //         "value": "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}",
        //         "errorMessage": "Invalid IP address format"
        //       },
        //       {
        //         "attributeName": "timestamp",
        //         "operator": "IS_NOT_FUTURE",
        //         "errorMessage": "Timestamp cannot be in the future"
        //       }
        //     ],
        //     "enum_values": [
        //       {
        //         "entityAttribute": "Entity.actionType",
        //         "enumName": "Actiontype Options",
        //         "value": "CREATE",
        //         "display": "Create",
        //         "description": "CREATE option",
        //         "sortOrder": 1,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.actionType",
        //         "enumName": "Actiontype Options",
        //         "value": "UPDATE",
        //         "display": "Update",
        //         "description": "UPDATE option",
        //         "sortOrder": 2,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.actionType",
        //         "enumName": "Actiontype Options",
        //         "value": "DELETE",
        //         "display": "Delete",
        //         "description": "DELETE option",
        //         "sortOrder": 3,
        //         "active": true
        //       },
        //       {
        //         "entityAttribute": "Entity.actionType",
        //         "enumName": "Actiontype Options",
        //         "value": "ACCESS",
        //         "display": "Access",
        //         "description": "ACCESS option",
        //         "sortOrder": 4,
        //         "active": true
        //       }
        //     ],
        //     "security_classification": [
        //       {
        //         "entityAttribute": "AuditLog.id",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.entityType",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.entityId",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.actionType",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.changeDetails",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.performedBy",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.ipAddress",
        //         "classification": "confidential",
        //         "piiType": "address",
        //         "encryptionRequired": true,
        //         "encryptionType": "aes256",
        //         "maskingRequired": true,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_restricted",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.userAgent",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       },
        //       {
        //         "entityAttribute": "AuditLog.timestamp",
        //         "classification": "internal",
        //         "piiType": "none",
        //         "encryptionRequired": false,
        //         "encryptionType": "none",
        //         "maskingRequired": false,
        //         "maskingPattern": "***",
        //         "accessLevel": "read_internal",
        //         "auditTrail": true
        //       }
        //     ],
        //     "system_permissions": [
        //       {
        //         "permissionId": "PERM_ENTITY_AUDITLOG",
        //         "permissionName": "AuditLog Entity Access",
        //         "permissionType": "entity",
        //         "resourceIdentifier": "AuditLog",
        //         "actions": ["create", "read", "update", "delete"],
        //         "description": "Full access to AuditLog entity",
        //         "scope": "tenant_records",
        //         "naturalLanguage": "Permission to manage AuditLog records",
        //         "version": 1,
        //         "status": "active"
        //       }
        //     ],
        //     "role_system_permissions": [
        //       {
        //         "roleId": "ROLE_ADMIN",
        //         "permissionId": "PERM_ENTITY_AUDITLOG",
        //         "grantedActions": ["create", "read", "update", "delete"],
        //         "rowLevelConditions": {"access_level": "tenant_records"},
        //         "naturalLanguage": "Admin has full access to AuditLog records"
        //       }
        //     ],
        //     "ui_properties": [],
        //     "confidence": 0.95,
        //     "extraction_method": "AI_Comprehensive",
        //     "completion_score": 100,
        //     "configuration_status": {
        //       "status": "complete",
        //       "completionPercentage": 85
        //     },
        //     "created_at": "2025-07-19T06:00:59",
        //     "updated_at": "2025-07-19T06:00:59"
        //   }
        // ];

        // return _parseEntitiesResponse(map);
        final response = await _makeApiCall(
          method: 'GET',
          url: fullUrl,
          options: options,
        );

        Logger.info('Job entities response status: ${response.statusCode}');

        if (response.statusCode == 200) {
          Logger.info('Job entities fetched successfully');
          return _parseEntitiesResponse(response.data);
        } else {
          final errorMessage =
              response.data['message'] ?? 'Failed to fetch job entities';
          Logger.error('Failed to fetch job entities: $errorMessage');
          throw Exception(errorMessage);
        }
      },
      operationName: 'Get job entities',
    );
  }

  /// Parse entities response with proper error handling
  ObjectCreationResponse _parseEntitiesResponse(dynamic responseData) {
    try {
      if (responseData is List) {
        final entities = responseData.map<ObjectCreationModel>((item) {
          if (item is Map<String, dynamic>) {
            return ObjectCreationModel.fromJson(item);
          } else {
            Logger.warning('Unexpected item type in list: ${item.runtimeType}');
            return ObjectCreationModel.fromJson({
              'displayName': item.toString(),
              'objectName': item.toString(),
            });
          }
        }).toList();

        return ObjectCreationResponse(
          success: true,
          data: entities,
          totalCount: entities.length,
        );
      } else if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('data') && responseData['data'] is List) {
          final entities =
              (responseData['data'] as List).map<ObjectCreationModel>((item) {
            if (item is Map<String, dynamic>) {
              return ObjectCreationModel.fromJson(item);
            } else {
              Logger.warning(
                  'Unexpected item type in data list: ${item.runtimeType}');
              return ObjectCreationModel.fromJson({
                'displayName': item.toString(),
                'objectName': item.toString(),
              });
            }
          }).toList();

          return ObjectCreationResponse(
            success: true,
            data: entities,
            totalCount: responseData['totalCount'] ?? entities.length,
            message: responseData['message']?.toString(),
          );
        } else {
          return ObjectCreationResponse(
            success: true,
            singleData: ObjectCreationModel.fromJson(responseData),
          );
        }
      } else {
        Logger.warning(
            'Unexpected response format: ${responseData.runtimeType}');
        throw Exception(
            'Unexpected response format: ${responseData.runtimeType}');
      }
    } catch (e) {
      Logger.error('Error parsing response data: $e');
      throw Exception('Error parsing response data: $e');
    }
  }

  /// Step 1: Create entity extraction job with user intent (NO RETRY - called from workflow)
  Future<Map<String, dynamic>> _createEntityExtractionJobInternal({
    required String userIntent,
  }) async {
    Logger.info('Creating entity extraction job with user intent');

    final fullUrl = '$_baseUrl$_extractEndpoint';
    final token = await _authService.getValidToken();

    final options = Options(
      headers: {
        'Content-Type': 'application/json',
        if (token != null) 'Authorization': 'Bearer $token',
      },
    );

    final requestData = {'userIntent': userIntent};
    // var map = {
    //   "id": "fff3a8c7-b507-47ba-ad62-b2b7058a5e6b",
    //   "user_intent":
    //       "I need to build a comprehensive CRM system. I need Contact entities with personal information like names, email addresses, phone numbers, job titles, and company affiliations.",
    //   "rephrased_intent":
    //       "Contact Management Requirements:\n\nThe system requires a robust Contact entity as the core component of the CRM system. Each Contact must store comprehensive personal identification information, including first and last names, along with multiple contact methods such as email addresses and phone numbers.\n\nAdditionally, the Contact entity needs to maintain professional information, specifically tracking the individual's current job title and their organizational affiliations. The system should establish clear relationships between Contacts and their associated companies.\n\nThese Contact records will serve as the foundation for the comprehensive CRM system, enabling effective relationship management and communication tracking.",
    //   "status": "completed",
    //   "progress": 100,
    //   "entities_extracted": 7,
    //   "total_attributes": 89,
    //   "total_rules": 29,
    //   "extraction_method": "AI_Comprehensive",
    //   "error_message": null,
    //   "retry_count": 0,
    //   "created_at": "2025-07-19T05:59:11",
    //   "updated_at": "2025-07-19T06:00:59",
    //   "completed_at": "2025-07-19T06:00:59.257882"
    // };
    // return {
    //   'success': true,
    //   'data': map,
    //   'jobId': map['id'],
    //   'status': map['status'],
    // };
    final response = await _makeApiCall(
      method: 'POST',
      url: fullUrl,
      data: requestData,
      options: options,
    );

    Logger.info(
        'Entity extraction job response status: ${response.statusCode}');

    if (response.statusCode == 200 || response.statusCode == 201) {
      Logger.info('Entity extraction job created successfully');
      return {
        'success': true,
        'data': response.data,
        'jobId': response.data['id'],
        'status': response.data['status'],
      };
    } else {
      final errorMessage =
          response.data['message'] ?? 'Failed to create entity extraction job';
      Logger.error('Failed to create entity extraction job: $errorMessage');
      throw Exception(errorMessage);
    }
  }

  /// Public method to create entity extraction job (WITH RETRY)
  // Future<Map<String, dynamic>> createEntityExtractionJob({
  //   required String userIntent,
  // }) async {
  //   return await _executeWithRetry<Map<String, dynamic>>(
  //     () => _createEntityExtractionJobInternal(userIntent: userIntent),
  //     operationName: 'Create entity extraction job',
  //   );
  // }

  /// Complete workflow: Create job and fetch entities directly
  /// The API now returns completed status immediately, so we can fetch entities right away
  Future<ObjectCreationResponse> executeCompleteExtractionWorkflow({
    required String userIntent,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Starting complete extraction workflow');

        // Step 1: Create entity extraction job (using internal method - no nested retry)
        final jobCreationResult =
            await _createEntityExtractionJobInternal(userIntent: userIntent);

        if (!jobCreationResult['success']) {
          throw Exception(
              jobCreationResult['message'] ?? 'Failed to create job');
        }

        final jobId = jobCreationResult['jobId'];
        Logger.info('Job created with ID: $jobId');

        // Step 2: Check if job is completed (API now returns completed status immediately)
        String jobStatus = jobCreationResult['status'] ?? 'pending';

        if (jobStatus != 'completed') {
          throw Exception('Job not completed. Status: $jobStatus');
        }

        Logger.info('Job completed successfully, fetching entities');

        // Step 3: Fetch the entities (this will have its own retry logic)
        final entitiesResponse = await getJobEntities(jobId);
        return entitiesResponse;
      },
      operationName: 'Complete extraction workflow',
    );
  }

  /// Update an existing entity
  Future<ObjectCreationResponse> updateEntity({
    required String jobId,
    required String entityId,
    required Map<String, dynamic> updateData,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Updating entity: $entityId for job: $jobId');

        final endpoint = '/api/v1/extraction/jobs/$jobId/entities/$entityId';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        final response = await _makeApiCall(
          method: 'PUT',
          url: fullUrl,
          data: updateData,
          options: options,
        );

        Logger.info('Update entity response status: ${response.statusCode}');

        if (response.statusCode == 200) {
          Logger.info('Entity updated successfully');
          return ObjectCreationResponse(
            success: true,
            singleData: ObjectCreationModel.fromJson(response.data),
            message: 'Entity updated successfully',
          );
        } else {
          final errorMessage =
              response.data['message'] ?? 'Failed to update entity';
          Logger.error('Failed to update entity: $errorMessage');
          throw Exception(errorMessage);
        }
      },
      operationName: 'Update entity',
    );
  }

  /// Delete an entity
  Future<ObjectCreationResponse> deleteEntity({
    required String jobId,
    required String entityId,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Deleting entity: $entityId for job: $jobId');

        final endpoint = '/api/v1/extraction/jobs/$jobId/entities/$entityId';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        final response = await _makeApiCall(
          method: 'DELETE',
          url: fullUrl,
          options: options,
        );

        Logger.info('Delete entity response status: ${response.statusCode}');

        if (response.statusCode == 200 || response.statusCode == 204) {
          Logger.info('Entity deleted successfully');
          return ObjectCreationResponse(
            success: true,
            message: 'Entity deleted successfully',
          );
        } else {
          final errorMessage =
              response.data['message'] ?? 'Failed to delete entity';
          Logger.error('Failed to delete entity: $errorMessage');
          throw Exception(errorMessage);
        }
      },
      operationName: 'Delete entity',
    );
  }

  @override
  Future<String?> getValidToken() async {
    return await _authService.getValidToken();
  }

  @override
  Future<String?> getUserId() async {
    return await _authService.getUserId();
  }
}
