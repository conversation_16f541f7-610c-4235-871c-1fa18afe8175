import 'package:flutter/material.dart';

/// Utility class for converting colors to and from JSON representations
class ColorUtils {
  /// Converts a Color to a hex string representation
  /// 
  /// Example: Color(0xFF0058FF) -> "#0058FF"
  static String? colorToHex(Color? color) {
    if (color == null) return null;
    
    // Extract RGB values
    final r = (color.red).toInt();
    final g = (color.green).toInt();
    final b = (color.blue).toInt();
    
    // Convert to hex string
    return '#${r.toRadixString(16).padLeft(2, '0').toUpperCase()}'
           '${g.toRadixString(16).padLeft(2, '0').toUpperCase()}'
           '${b.toRadixString(16).padLeft(2, '0').toUpperCase()}';
  }

  /// Converts a hex string to a Color
  /// 
  /// Supports formats: "#RGB", "#RRGGBB", "#AARRGGBB"
  /// Also supports named colors like "red", "blue", etc.
  static Color? hexToColor(String? hexString) {
    if (hexString == null || hexString.isEmpty) return null;
    
    // Handle named colors first
    final namedColor = _getNamedColor(hexString.toLowerCase());
    if (namedColor != null) return namedColor;
    
    // Remove # if present
    String hex = hexString.replaceAll('#', '');
    
    // Handle different hex formats
    if (hex.length == 3) {
      // RGB -> RRGGBB
      hex = hex.split('').map((char) => char + char).join();
    }
    
    if (hex.length == 6) {
      // RRGGBB -> FFRRGGBB (add full alpha)
      hex = 'FF' + hex;
    }
    
    if (hex.length == 8) {
      try {
        return Color(int.parse(hex, radix: 16));
      } catch (e) {
        return null;
      }
    }
    
    return null;
  }

  /// Converts a Color to an ARGB map
  /// 
  /// Example: Color(0xFF0058FF) -> {"a": 255, "r": 0, "g": 88, "b": 255}
  static Map<String, int>? colorToArgb(Color? color) {
    if (color == null) return null;
    
    return {
      'a': color.alpha,
      'r': color.red,
      'g': color.green,
      'b': color.blue,
    };
  }

  /// Converts an ARGB map to a Color
  /// 
  /// Example: {"a": 255, "r": 0, "g": 88, "b": 255} -> Color(0xFF0058FF)
  static Color? argbToColor(Map<String, dynamic>? argb) {
    if (argb == null) return null;
    
    try {
      final a = argb['a'] as int? ?? 255;
      final r = argb['r'] as int? ?? 0;
      final g = argb['g'] as int? ?? 0;
      final b = argb['b'] as int? ?? 0;
      
      return Color.fromARGB(a, r, g, b);
    } catch (e) {
      return null;
    }
  }

  /// Converts a Color to a JSON-friendly representation
  /// 
  /// Returns either a hex string for simple colors or an ARGB map for colors with transparency
  static dynamic colorToJson(Color? color) {
    if (color == null) return null;
    
    // If the color has full opacity, use hex string for simplicity
    if (color.alpha == 255) {
      return colorToHex(color);
    }
    
    // Otherwise, use ARGB map to preserve alpha channel
    return colorToArgb(color);
  }

  /// Converts a JSON representation back to a Color
  /// 
  /// Handles both hex strings and ARGB maps
  static Color? jsonToColor(dynamic json) {
    if (json == null) return null;
    
    if (json is String) {
      return hexToColor(json);
    }
    
    if (json is Map<String, dynamic>) {
      return argbToColor(json);
    }
    
    return null;
  }

  /// Gets a named color by string
  static Color? _getNamedColor(String name) {
    switch (name) {
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.yellow;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'pink':
        return Colors.pink;
      case 'cyan':
        return Colors.cyan;
      case 'teal':
        return Colors.teal;
      case 'indigo':
        return Colors.indigo;
      case 'lime':
        return Colors.lime;
      case 'amber':
        return Colors.amber;
      case 'brown':
        return Colors.brown;
      case 'grey':
      case 'gray':
        return Colors.grey;
      case 'black':
        return Colors.black;
      case 'white':
        return Colors.white;
      case 'transparent':
        return Colors.transparent;
      default:
        return null;
    }
  }

  /// Gets the name of a color if it matches a standard Material color
  static String? getColorName(Color? color) {
    if (color == null) return null;
    
    final colorMap = {
      Colors.red: 'red',
      Colors.blue: 'blue',
      Colors.green: 'green',
      Colors.yellow: 'yellow',
      Colors.orange: 'orange',
      Colors.purple: 'purple',
      Colors.pink: 'pink',
      Colors.cyan: 'cyan',
      Colors.teal: 'teal',
      Colors.indigo: 'indigo',
      Colors.lime: 'lime',
      Colors.amber: 'amber',
      Colors.brown: 'brown',
      Colors.grey: 'grey',
      Colors.black: 'black',
      Colors.white: 'white',
      Colors.transparent: 'transparent',
    };
    
    return colorMap[color];
  }
}
