import 'package:flutter/material.dart';
import 'dart:convert';
import '../widgets/calendar_widget.dart';
import 'flexible_widget_serializer.dart';

/// Runtime Calendar Widget - Uses runtime property discovery instead of custom serialization
/// This demonstrates the difference between custom serialization vs runtime discovery
class RuntimeCalendarWidget extends StatelessWidget {
  final CalendarSize size;
  final DateTime? initialSelectedDate;
  final double? borderRadius;
  final double? borderWidth;
  final bool hasBorder;
  final bool hasShadow;
  final Color? backgroundColor;
  final Color? headerColor;
  final Color? selectedDateColor;
  final Color? todayColor;
  final Color? headerTextColor;
  final Color? weekdayLabelColor;
  final Color? dateTextColor;
  final double? width;
  final double? height;
  final bool showHeader;
  final bool showNavigation;
  final bool showWeekdayLabels;
  final bool allowDateSelection;
  final bool allowMultipleSelection;
  final int firstDayOfWeek;
  final Map<DateTime, List<CalendarEvent>>? events;
  final Function(DateTime)? onDateSelected;
  final Function(DateTime)? onMonthChanged;

  const RuntimeCalendarWidget({
    Key? key,
    this.size = CalendarSize.medium,
    this.initialSelectedDate,
    this.borderRadius,
    this.borderWidth,
    this.hasBorder = false,
    this.hasShadow = false,
    this.backgroundColor,
    this.headerColor,
    this.selectedDateColor,
    this.todayColor,
    this.headerTextColor,
    this.weekdayLabelColor,
    this.dateTextColor,
    this.width,
    this.height,
    this.showHeader = true,
    this.showNavigation = true,
    this.showWeekdayLabels = true,
    this.allowDateSelection = true,
    this.allowMultipleSelection = false,
    this.firstDayOfWeek = 1,
    this.events,
    this.onDateSelected,
    this.onMonthChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Build the calendar using standard Flutter widgets
    // This will be serialized using runtime discovery
    return Container(
      width: width ?? _getDefaultWidth(),
      height: height ?? _getDefaultHeight(),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        border: hasBorder
            ? Border.all(
                color: Colors.grey.shade300,
                width: borderWidth ?? 1.0,
              )
            : null,
        borderRadius: BorderRadius.circular(borderRadius ?? 8.0),
        boxShadow: hasShadow
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Column(
        children: [
          if (showHeader) _buildHeader(),
          if (showWeekdayLabels) _buildWeekdayLabels(),
          Expanded(child: _buildCalendarGrid()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: headerColor ?? Colors.blue.shade50,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (showNavigation)
            IconButton(
              onPressed: () {
                // Previous month logic would go here
              },
              icon: const Icon(Icons.chevron_left),
              color: headerTextColor ?? Colors.black,
            ),
          Text(
            _formatHeaderDate(),
            style: TextStyle(
              fontSize: _getDefaultHeadingFontSize(),
              fontWeight: FontWeight.w600,
              color: headerTextColor ?? Colors.black,
            ),
          ),
          if (showNavigation)
            IconButton(
              onPressed: () {
                // Next month logic would go here
              },
              icon: const Icon(Icons.chevron_right),
              color: headerTextColor ?? Colors.black,
            ),
        ],
      ),
    );
  }

  Widget _buildWeekdayLabels() {
    final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: weekdays.map((weekday) =>
          Expanded(
            child: Text(
              weekday,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: _getDefaultLabelFontSize(),
                fontWeight: FontWeight.w500,
                color: weekdayLabelColor ?? Colors.grey.shade600,
              ),
            ),
          ),
        ).toList(),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    // Simple calendar grid for demonstration
    List<Widget> weeks = [];
    
    for (int week = 0; week < 6; week++) {
      List<Widget> days = [];
      
      for (int day = 0; day < 7; day++) {
        final dayNumber = week * 7 + day + 1;
        if (dayNumber <= 31) { // Simplified - real implementation would calculate properly
          days.add(_buildDateCell(dayNumber));
        } else {
          days.add(const SizedBox());
        }
      }
      
      weeks.add(
        Row(
          children: days.map((day) => Expanded(child: day)).toList(),
        ),
      );
    }

    return Column(
      children: weeks.map((week) => Expanded(child: week)).toList(),
    );
  }

  Widget _buildDateCell(int dayNumber) {
    final isToday = dayNumber == DateTime.now().day;
    final isSelected = dayNumber == (initialSelectedDate?.day ?? -1);
    
    return Container(
      margin: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: isSelected
            ? selectedDateColor ?? Colors.blue
            : isToday
                ? todayColor ?? Colors.orange.shade100
                : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 0.5,
        ),
      ),
      child: Center(
        child: Text(
          dayNumber.toString(),
          style: TextStyle(
            fontSize: _getDefaultBodyFontSize(),
            fontWeight: FontWeight.normal,
            color: isSelected
                ? Colors.white
                : isToday
                    ? Colors.orange.shade800
                    : dateTextColor ?? Colors.black,
          ),
        ),
      ),
    );
  }

  // Helper methods for default values
  double _getDefaultWidth() {
    switch (size) {
      case CalendarSize.small:
        return 280;
      case CalendarSize.medium:
        return 350;
      case CalendarSize.large:
        return 420;
    }
  }

  double _getDefaultHeight() {
    switch (size) {
      case CalendarSize.small:
        return 240;
      case CalendarSize.medium:
        return 300;
      case CalendarSize.large:
        return 360;
    }
  }

  double _getDefaultHeadingFontSize() {
    switch (size) {
      case CalendarSize.small:
        return 14.0;
      case CalendarSize.medium:
        return 16.0;
      case CalendarSize.large:
        return 18.0;
    }
  }

  double _getDefaultBodyFontSize() {
    switch (size) {
      case CalendarSize.small:
        return 12.0;
      case CalendarSize.medium:
        return 14.0;
      case CalendarSize.large:
        return 16.0;
    }
  }

  double _getDefaultLabelFontSize() {
    switch (size) {
      case CalendarSize.small:
        return 10.0;
      case CalendarSize.medium:
        return 12.0;
      case CalendarSize.large:
        return 12.0;
    }
  }

  String _formatHeaderDate() {
    final now = DateTime.now();
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[now.month - 1]} ${now.year}';
  }

  /// Runtime serialization using FlexibleWidgetSerializer
  Map<String, dynamic> toRuntimeJson() {
    return FlexibleWidgetSerializer.serialize(this);
  }

  /// Convert to JSON string using runtime discovery
  String toRuntimeJsonString({bool prettyPrint = false}) {
    return FlexibleWidgetSerializer.toJsonString(this, prettyPrint: prettyPrint);
  }

  /// Get serialization statistics
  Map<String, dynamic> getSerializationStats() {
    return FlexibleWidgetSerializer.getSerializationStats(this);
  }

  /// Create RuntimeCalendarWidget from runtime-generated JSON
  /// This demonstrates how to deserialize runtime discovery JSON
  static RuntimeCalendarWidget fromRuntimeJson(Map<String, dynamic> json) {
    return RuntimeCalendarWidget(
      size: _parseCalendarSize(json['size']),
      initialSelectedDate: json['initialSelectedDate'] != null
          ? DateTime.parse(json['initialSelectedDate'])
          : null,
      borderRadius: json['borderRadius']?.toDouble(),
      borderWidth: json['borderWidth']?.toDouble(),
      hasBorder: json['hasBorder'] ?? false,
      hasShadow: json['hasShadow'] ?? false,
      backgroundColor: _parseColor(json['backgroundColor']),
      headerColor: _parseColor(json['headerColor']),
      selectedDateColor: _parseColor(json['selectedDateColor']),
      todayColor: _parseColor(json['todayColor']),
      headerTextColor: _parseColor(json['headerTextColor']),
      weekdayLabelColor: _parseColor(json['weekdayLabelColor']),
      dateTextColor: _parseColor(json['dateTextColor']),
      width: json['width']?.toDouble(),
      height: json['height']?.toDouble(),
      showHeader: json['showHeader'] ?? true,
      showNavigation: json['showNavigation'] ?? true,
      showWeekdayLabels: json['showWeekdayLabels'] ?? true,
      allowDateSelection: json['allowDateSelection'] ?? true,
      allowMultipleSelection: json['allowMultipleSelection'] ?? false,
      firstDayOfWeek: json['firstDayOfWeek'] ?? 1,
      events: _parseEvents(json['events']),
    );
  }

  /// Helper: Parse CalendarSize from string
  static CalendarSize _parseCalendarSize(dynamic sizeValue) {
    if (sizeValue == null) return CalendarSize.medium;

    switch (sizeValue.toString().toLowerCase()) {
      case 'small':
        return CalendarSize.small;
      case 'medium':
        return CalendarSize.medium;
      case 'large':
        return CalendarSize.large;
      default:
        return CalendarSize.medium;
    }
  }

  /// Helper: Parse Color from runtime JSON (handles both hex strings and int values)
  static Color? _parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is int) {
      // Runtime discovery stores colors as int values
      return Color(colorValue);
    } else if (colorValue is String) {
      // Handle hex color strings
      if (colorValue.startsWith('#')) {
        return Color(int.parse(colorValue.substring(1), radix: 16) + 0xFF000000);
      }
    }

    return null;
  }

  /// Helper: Parse events from runtime JSON
  static Map<DateTime, List<CalendarEvent>>? _parseEvents(dynamic eventsValue) {
    if (eventsValue == null) return null;

    Map<DateTime, List<CalendarEvent>> events = {};

    if (eventsValue is Map<String, dynamic>) {
      eventsValue.forEach((dateStr, eventList) {
        try {
          final date = DateTime.parse(dateStr);
          final List<CalendarEvent> parsedEvents = [];

          if (eventList is List) {
            for (var eventData in eventList) {
              if (eventData is Map<String, dynamic>) {
                parsedEvents.add(CalendarEvent(
                  title: eventData['title'] ?? '',
                  description: eventData['description'],
                  color: _parseColor(eventData['color']),
                  startTime: eventData['startTime'] != null
                      ? DateTime.parse(eventData['startTime'])
                      : null,
                  endTime: eventData['endTime'] != null
                      ? DateTime.parse(eventData['endTime'])
                      : null,
                ));
              }
            }
          }

          events[date] = parsedEvents;
        } catch (e) {
          // Skip invalid date entries
        }
      });
    }

    return events.isEmpty ? null : events;
  }

  /// Create RuntimeCalendarWidget from runtime JSON string
  static RuntimeCalendarWidget fromRuntimeJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return fromRuntimeJson(json);
  }
}
