import 'dart:convert';

/// Model for object creation/extraction entities
class ObjectCreationModel {
  final String? id;
  final String? name;
  final String? displayName;
  final String? type;
  final String? description;
  final String? businessPurpose;
  final String? businessDomain;
  final String? category;
  final String? archivalStrategy;
  final String? colorTheme;
  final String? icon;
  final List<String>? tags;
  final List<ObjectAttribute>? attributes;
  final List<EntityRelationship>? relationships;
  final List<BusinessRule>? businessRules;
  final List<EnumValue>? enumValues;
  final List<SecurityClassification>? securityClassification;
  final List<SystemPermission>? systemPermissions;
  final List<RoleSystemPermission>? roleSystemPermissions;
  final List<dynamic>? uiProperties;
  final double? confidence;
  final String? extractionMethod;
  final int? completionScore;
  final ConfigurationStatus? configurationStatus;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ObjectCreationModel({
    this.id,
    this.name,
    this.displayName,
    this.type,
    this.description,
    this.businessPurpose,
    this.businessDomain,
    this.category,
    this.archivalStrategy,
    this.colorTheme,
    this.icon,
    this.tags,
    this.attributes,
    this.relationships,
    this.businessRules,
    this.enumValues,
    this.securityClassification,
    this.systemPermissions,
    this.roleSystemPermissions,
    this.uiProperties,
    this.confidence,
    this.extractionMethod,
    this.completionScore,
    this.configurationStatus,
    this.createdAt,
    this.updatedAt,
  });

  /// Enhanced factory method with better error handling and fallback support
  factory ObjectCreationModel.fromJson(Map<String, dynamic> json) {
    try {
      return ObjectCreationModel(
        id: json['id']?.toString(),
        name: json['name']?.toString() ?? json['objectName']?.toString(),
        displayName: json['displayName']?.toString() ??
            json['name']?.toString() ??
            json['objectName']?.toString(),
        type: json['type']?.toString(),
        description: json['description']?.toString(),
        businessPurpose: json['businessPurpose']?.toString(),
        businessDomain: json['businessDomain']?.toString(),
        category: json['category']?.toString(),
        archivalStrategy: json['archivalStrategy']?.toString(),
        colorTheme: json['colorTheme']?.toString(),
        icon: json['icon']?.toString(),
        tags: _safeParseStringList(json['tags']),
        attributes: _safeParseList<ObjectAttribute>(
            json['attributes'], (item) => ObjectAttribute.fromJson(item)),
        relationships: _safeParseList<EntityRelationship>(
            json['relationships'], (item) => EntityRelationship.fromJson(item)),
        businessRules: _safeParseList<BusinessRule>(
            json['business_rules'] ?? json['businessRules'],
            (item) => BusinessRule.fromJson(item)),
        enumValues: _safeParseList<EnumValue>(
            json['enum_values'] ?? json['enumValues'],
            (item) => EnumValue.fromJson(item)),
        securityClassification: _safeParseList<SecurityClassification>(
            json['security_classification'] ?? json['securityClassification'],
            (item) => SecurityClassification.fromJson(item)),
        systemPermissions: _safeParseList<SystemPermission>(
            json['system_permissions'] ?? json['systemPermissions'],
            (item) => SystemPermission.fromJson(item)),
        roleSystemPermissions: _safeParseList<RoleSystemPermission>(
            json['role_system_permissions'] ?? json['roleSystemPermissions'],
            (item) => RoleSystemPermission.fromJson(item)),
        uiProperties: json['ui_properties'] ?? json['uiProperties'],
        confidence: _safeParseDouble(json['confidence']),
        extractionMethod: json['extraction_method']?.toString() ??
            json['extractionMethod']?.toString(),
        completionScore:
            _safeParseInt(json['completion_score'] ?? json['completionScore']),
        configurationStatus: json['configuration_status'] != null
            ? ConfigurationStatus.fromJson(json['configuration_status'])
            : json['configurationStatus'] != null
                ? ConfigurationStatus.fromJson(json['configurationStatus'])
                : null,
        createdAt: _safeParseDateTime(json['created_at'] ?? json['createdAt']),
        updatedAt: _safeParseDateTime(json['updated_at'] ?? json['updatedAt']),
      );
    } catch (e) {
      // If parsing fails, create a minimal object with available data
      return ObjectCreationModel(
        id: json['id']?.toString(),
        name: json['name']?.toString() ??
            json['objectName']?.toString() ??
            'Unknown Object',
        displayName: json['displayName']?.toString() ??
            json['name']?.toString() ??
            json['objectName']?.toString() ??
            'Unknown Object',
        description:
            json['description']?.toString() ?? 'No description available',
      );
    }
  }

  /// Safe string list parsing
  static List<String>? _safeParseStringList(dynamic value) {
    try {
      if (value == null) return null;
      if (value is List) {
        return value.map((item) => item.toString()).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe list parsing with transformation
  static List<T>? _safeParseList<T>(
      dynamic value, T Function(dynamic) transform) {
    try {
      if (value == null) return null;
      if (value is List) {
        final List<T> result = [];
        for (final item in value) {
          try {
            result.add(transform(item));
          } catch (e) {
            // Skip invalid items but continue processing
            continue;
          }
        }
        return result.isEmpty ? null : result;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe double parsing
  static double? _safeParseDouble(dynamic value) {
    try {
      if (value == null) return null;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe int parsing
  static int? _safeParseInt(dynamic value) {
    try {
      if (value == null) return null;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe DateTime parsing
  static DateTime? _safeParseDateTime(dynamic value) {
    try {
      if (value == null) return null;
      if (value is String) return DateTime.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'type': type,
      'description': description,
      'businessPurpose': businessPurpose,
      'businessDomain': businessDomain,
      'category': category,
      'archivalStrategy': archivalStrategy,
      'colorTheme': colorTheme,
      'icon': icon,
      'tags': tags,
      'attributes': attributes?.map((attr) => attr.toJson()).toList(),
      'relationships': relationships?.map((rel) => rel.toJson()).toList(),
      'business_rules': businessRules?.map((rule) => rule.toJson()).toList(),
      'enum_values': enumValues?.map((enumVal) => enumVal.toJson()).toList(),
      'security_classification':
          securityClassification?.map((sec) => sec.toJson()).toList(),
      'system_permissions':
          systemPermissions?.map((perm) => perm.toJson()).toList(),
      'role_system_permissions':
          roleSystemPermissions?.map((rolePerm) => rolePerm.toJson()).toList(),
      'ui_properties': uiProperties,
      'confidence': confidence,
      'extraction_method': extractionMethod,
      'completion_score': completionScore,
      'configuration_status': configurationStatus?.toJson(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'ObjectCreationModel(id: $id, name: $name, displayName: $displayName, type: $type)';
  }
}

/// Model for object attributes
class ObjectAttribute {
  final String? name;
  final String? displayName;
  final String? dataType;
  final bool? required;
  final bool? unique;
  final String? defaultType;
  final String? defaultValue;
  final String? description;
  final String? helperText;
  final List<String>? enumValues;
  final ValidationRule? validation;

  ObjectAttribute({
    this.name,
    this.displayName,
    this.dataType,
    this.required,
    this.unique,
    this.defaultType,
    this.defaultValue,
    this.description,
    this.helperText,
    this.enumValues,
    this.validation,
  });

  factory ObjectAttribute.fromJson(Map<String, dynamic> json) {
    return ObjectAttribute(
      name: json['name']?.toString(),
      displayName: json['displayName']?.toString(),
      dataType: json['dataType']?.toString(),
      required: json['required'] is bool ? json['required'] : false,
      unique: json['unique'] is bool ? json['unique'] : false,
      defaultType: json['defaultType']?.toString(),
      defaultValue: json['defaultValue']?.toString(),
      description: json['description']?.toString(),
      helperText: json['helperText']?.toString(),
      enumValues: json['enumValues'] != null
          ? List<String>.from(json['enumValues'])
          : null,
      validation: json['validation'] != null
          ? ValidationRule.fromJson(json['validation'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'dataType': dataType,
      'required': required,
      'unique': unique,
      'defaultType': defaultType,
      'defaultValue': defaultValue,
      'description': description,
      'helperText': helperText,
      'enumValues': enumValues,
      'validation': validation?.toJson(),
    };
  }
}

/// Model for entity relationships
class EntityRelationship {
  final String? relatedEntity;
  final String? relationshipType;
  final String? foreignKey;
  final String? joinTable;
  final String? description;

  EntityRelationship({
    this.relatedEntity,
    this.relationshipType,
    this.foreignKey,
    this.joinTable,
    this.description,
  });

  factory EntityRelationship.fromJson(Map<String, dynamic> json) {
    return EntityRelationship(
      relatedEntity: json['relatedEntity']?.toString(),
      relationshipType: json['relationshipType']?.toString(),
      foreignKey: json['foreignKey']?.toString(),
      joinTable: json['joinTable']?.toString(),
      description: json['description']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'relatedEntity': relatedEntity,
      'relationshipType': relationshipType,
      'foreignKey': foreignKey,
      'joinTable': joinTable,
      'description': description,
    };
  }
}

/// Model for business rules
class BusinessRule {
  final String? attributeName;
  final String? operator;
  final String? pattern;
  final String? errorMessage;
  final double? minValue;
  final double? maxValue;

  BusinessRule({
    this.attributeName,
    this.operator,
    this.pattern,
    this.errorMessage,
    this.minValue,
    this.maxValue,
  });

  factory BusinessRule.fromJson(Map<String, dynamic> json) {
    return BusinessRule(
      attributeName: json['attributeName']?.toString(),
      operator: json['operator']?.toString(),
      pattern: json['pattern']?.toString(),
      errorMessage: json['errorMessage']?.toString(),
      minValue: json['minValue']?.toDouble(),
      maxValue: json['maxValue']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attributeName': attributeName,
      'operator': operator,
      'pattern': pattern,
      'errorMessage': errorMessage,
      'minValue': minValue,
      'maxValue': maxValue,
    };
  }
}

/// Model for enum values
class EnumValue {
  final String? entityAttribute;
  final String? enumName;
  final String? value;
  final String? display;
  final String? description;
  final int? sortOrder;
  final bool? active;

  EnumValue({
    this.entityAttribute,
    this.enumName,
    this.value,
    this.display,
    this.description,
    this.sortOrder,
    this.active,
  });

  factory EnumValue.fromJson(Map<String, dynamic> json) {
    return EnumValue(
      entityAttribute: json['entityAttribute']?.toString(),
      enumName: json['enumName']?.toString(),
      value: json['value']?.toString(),
      display: json['display']?.toString(),
      description: json['description']?.toString(),
      sortOrder: json['sortOrder']?.toInt(),
      active: json['active'] is bool ? json['active'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityAttribute': entityAttribute,
      'enumName': enumName,
      'value': value,
      'display': display,
      'description': description,
      'sortOrder': sortOrder,
      'active': active,
    };
  }
}

/// Model for security classification
class SecurityClassification {
  final String? entityAttribute;
  final String? classification;
  final String? piiType;
  final bool? encryptionRequired;
  final String? encryptionType;
  final bool? maskingRequired;
  final String? maskingPattern;
  final String? accessLevel;
  final bool? auditTrail;

  SecurityClassification({
    this.entityAttribute,
    this.classification,
    this.piiType,
    this.encryptionRequired,
    this.encryptionType,
    this.maskingRequired,
    this.maskingPattern,
    this.accessLevel,
    this.auditTrail,
  });

  factory SecurityClassification.fromJson(Map<String, dynamic> json) {
    return SecurityClassification(
      entityAttribute: json['entityAttribute']?.toString(),
      classification: json['classification']?.toString(),
      piiType: json['piiType']?.toString(),
      encryptionRequired: json['encryptionRequired'] is bool
          ? json['encryptionRequired']
          : false,
      encryptionType: json['encryptionType']?.toString(),
      maskingRequired:
          json['maskingRequired'] is bool ? json['maskingRequired'] : false,
      maskingPattern: json['maskingPattern']?.toString(),
      accessLevel: json['accessLevel']?.toString(),
      auditTrail: json['auditTrail'] is bool ? json['auditTrail'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityAttribute': entityAttribute,
      'classification': classification,
      'piiType': piiType,
      'encryptionRequired': encryptionRequired,
      'encryptionType': encryptionType,
      'maskingRequired': maskingRequired,
      'maskingPattern': maskingPattern,
      'accessLevel': accessLevel,
      'auditTrail': auditTrail,
    };
  }
}

/// Model for system permissions
class SystemPermission {
  final String? permissionId;
  final String? permissionName;
  final String? permissionType;
  final String? resourceIdentifier;
  final List<String>? actions;
  final String? description;
  final String? scope;
  final String? naturalLanguage;
  final int? version;
  final String? status;

  SystemPermission({
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.actions,
    this.description,
    this.scope,
    this.naturalLanguage,
    this.version,
    this.status,
  });

  factory SystemPermission.fromJson(Map<String, dynamic> json) {
    return SystemPermission(
      permissionId: json['permissionId']?.toString(),
      permissionName: json['permissionName']?.toString(),
      permissionType: json['permissionType']?.toString(),
      resourceIdentifier: json['resourceIdentifier']?.toString(),
      actions:
          json['actions'] != null ? List<String>.from(json['actions']) : null,
      description: json['description']?.toString(),
      scope: json['scope']?.toString(),
      naturalLanguage: json['naturalLanguage']?.toString(),
      version: json['version']?.toInt(),
      status: json['status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'permissionId': permissionId,
      'permissionName': permissionName,
      'permissionType': permissionType,
      'resourceIdentifier': resourceIdentifier,
      'actions': actions,
      'description': description,
      'scope': scope,
      'naturalLanguage': naturalLanguage,
      'version': version,
      'status': status,
    };
  }
}

/// Model for role system permissions
class RoleSystemPermission {
  final String? roleId;
  final String? permissionId;
  final List<String>? grantedActions;
  final Map<String, dynamic>? rowLevelConditions;
  final String? naturalLanguage;

  RoleSystemPermission({
    this.roleId,
    this.permissionId,
    this.grantedActions,
    this.rowLevelConditions,
    this.naturalLanguage,
  });

  factory RoleSystemPermission.fromJson(Map<String, dynamic> json) {
    return RoleSystemPermission(
      roleId: json['roleId']?.toString(),
      permissionId: json['permissionId']?.toString(),
      grantedActions: json['grantedActions'] != null
          ? List<String>.from(json['grantedActions'])
          : null,
      rowLevelConditions: json['rowLevelConditions'] as Map<String, dynamic>?,
      naturalLanguage: json['naturalLanguage']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roleId': roleId,
      'permissionId': permissionId,
      'grantedActions': grantedActions,
      'rowLevelConditions': rowLevelConditions,
      'naturalLanguage': naturalLanguage,
    };
  }
}

/// Model for configuration status
class ConfigurationStatus {
  final String? status;
  final int? completionPercentage;

  ConfigurationStatus({
    this.status,
    this.completionPercentage,
  });

  factory ConfigurationStatus.fromJson(Map<String, dynamic> json) {
    return ConfigurationStatus(
      status: json['status']?.toString(),
      completionPercentage: json['completionPercentage']?.toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'completionPercentage': completionPercentage,
    };
  }
}

/// Model for validation rules
class ValidationRule {
  final bool? required;

  ValidationRule({
    this.required,
  });

  factory ValidationRule.fromJson(Map<String, dynamic> json) {
    return ValidationRule(
      required: json['required'] is bool ? json['required'] : false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'required': required,
    };
  }
}

/// Response model for API calls
class ObjectCreationResponse {
  final bool success;
  final String? message;
  final List<ObjectCreationModel>? data;
  final ObjectCreationModel? singleData;
  final int? totalCount;

  ObjectCreationResponse({
    required this.success,
    this.message,
    this.data,
    this.singleData,
    this.totalCount,
  });

  /// Enhanced factory method with better error handling
  factory ObjectCreationResponse.fromJson(Map<String, dynamic> json) {
    try {
      return ObjectCreationResponse(
        success: json['success'] ?? false,
        message: json['message']?.toString(),
        data: _safeParseDataList(json['data']),
        singleData: _safeParseSingleData(json['data']),
        totalCount: _safeParseInt(json['totalCount']),
      );
    } catch (e) {
      // Return error response if parsing fails
      return ObjectCreationResponse(
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  /// Safe parsing of data list
  static List<ObjectCreationModel>? _safeParseDataList(dynamic data) {
    try {
      if (data == null) return null;
      if (data is List) {
        final List<ObjectCreationModel> result = [];
        for (final item in data) {
          try {
            if (item is Map<String, dynamic>) {
              result.add(ObjectCreationModel.fromJson(item));
            }
          } catch (e) {
            // Skip invalid items but continue processing
            continue;
          }
        }
        return result.isEmpty ? null : result;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe parsing of single data object
  static ObjectCreationModel? _safeParseSingleData(dynamic data) {
    try {
      if (data != null && data is Map<String, dynamic>) {
        return ObjectCreationModel.fromJson(data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safe int parsing
  static int? _safeParseInt(dynamic value) {
    try {
      if (value == null) return null;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value);
      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data':
          data?.map((item) => item.toJson()).toList() ?? singleData?.toJson(),
      'totalCount': totalCount,
    };
  }
}
