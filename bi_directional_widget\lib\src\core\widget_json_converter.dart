import 'package:flutter/material.dart';
import 'dart:convert';

/// Abstract base class for widgets that support bidirectional JSON conversion
abstract class WidgetJsonConverter<T extends Widget> {
  /// Converts a widget instance to JSON
  /// 
  /// This method should extract all the configuration properties from the widget
  /// and return them as a JSON-serializable Map.
  Map<String, dynamic> widgetToJson(T widget);

  /// Creates a widget instance from JSON
  /// 
  /// This method should parse the JSON configuration and create a new widget
  /// instance with the specified properties.
  T jsonToWidget(Map<String, dynamic> json);

  /// Validates that the JSON contains all required fields for widget creation
  /// 
  /// Returns true if the JSON is valid, false otherwise.
  /// Optionally returns validation errors in the errors list.
  bool validateJson(Map<String, dynamic> json, {List<String>? errors});

  /// Gets the widget type identifier
  /// 
  /// This should return a unique string identifier for the widget type
  /// that can be used in JSON to identify which converter to use.
  String get widgetType;

  /// Gets the JSON schema for this widget type
  /// 
  /// Returns a JSON schema that describes the expected structure
  /// of the JSON configuration for this widget.
  Map<String, dynamic> get jsonSchema;

  /// Converts a JSON string to a widget
  T jsonStringToWidget(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return jsonToWidget(json);
  }

  /// Converts a widget to a JSON string
  String widgetToJsonString(T widget, {bool prettyPrint = false}) {
    final Map<String, dynamic> json = widgetToJson(widget);
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }

  /// Creates a deep copy of a widget by converting to JSON and back
  T cloneWidget(T widget) {
    final json = widgetToJson(widget);
    return jsonToWidget(json);
  }

  /// Compares two widgets by converting them to JSON and comparing the JSON
  bool compareWidgets(T widget1, T widget2) {
    final json1 = widgetToJson(widget1);
    final json2 = widgetToJson(widget2);
    return _deepEquals(json1, json2);
  }

  /// Deep equality check for JSON objects
  bool _deepEquals(dynamic a, dynamic b) {
    if (a.runtimeType != b.runtimeType) return false;
    
    if (a is Map && b is Map) {
      if (a.length != b.length) return false;
      for (final key in a.keys) {
        if (!b.containsKey(key) || !_deepEquals(a[key], b[key])) {
          return false;
        }
      }
      return true;
    }
    
    if (a is List && b is List) {
      if (a.length != b.length) return false;
      for (int i = 0; i < a.length; i++) {
        if (!_deepEquals(a[i], b[i])) return false;
      }
      return true;
    }
    
    return a == b;
  }
}
