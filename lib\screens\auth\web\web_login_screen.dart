import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/responsive_state_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:nsl/screens/web/new_design/greeting_helper.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';
import '../../../l10n/app_localizations.dart';
import '../../../providers/auth_provider.dart';
import '../../../ui_components/inputs/app_text_field.dart';
import '../../../ui_components/theme/app_theme.dart';
import '../../../theme/spacing.dart';
import '../../../utils/logger.dart';
import '../../../utils/navigation_service.dart';
import '../../../utils/validators.dart';
import '../../../widgets/auth/auth_button.dart';
import '../../../widgets/auth/auth_link.dart';
import '../../../widgets/auth/password_field.dart';
import '../../../widgets/auth/validated_text_field.dart';
import '../../../widgets/responsive_register_builder.dart';

class WebLoginScreen extends StatefulWidget {
  const WebLoginScreen({super.key});

  @override
  State<WebLoginScreen> createState() => _WebLoginScreenState();
}

class _WebLoginScreenState extends State<WebLoginScreen> {
  final _emailController = TextEditingController(text: "testuser12");
  final _passwordController = TextEditingController(text: "test@123");

  // final _emailController = TextEditingController(text: "mentor");
  // final _passwordController = TextEditingController(text: "mentor@123");

  final _formKey = GlobalKey<FormState>();
  bool _rememberMe = false;
  bool _isManualLoading = false; // Manual loading state for testing

  @override
  void initState() {
    super.initState();
    Logger.info('WebLoginScreen initialized');

    // Check if we need to pre-fill the form with saved credentials
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      Logger.info('WebLoginScreen post-frame callback');
      Logger.info(
          'AuthProvider state - Remember Me: ${authProvider.rememberMe}');
      Logger.info(
          'AuthProvider state - Saved Email: ${authProvider.savedEmail}');
      Logger.info(
          'AuthProvider state - Has Password: ${authProvider.savedPassword != null}');

      // Pre-fill form fields
      setState(() {
        _rememberMe = authProvider.rememberMe;
        Logger.info('Setting Remember Me checkbox to: $_rememberMe');

        // Pre-fill email and password if remember me is checked
        if (_rememberMe &&
            authProvider.savedEmail != null &&
            _emailController.text.isEmpty) {
          _emailController.text = authProvider.savedEmail!;
          Logger.info('Pre-filled email: ${_emailController.text}');

          if (authProvider.savedPassword != null) {
            _passwordController.text = authProvider.savedPassword!;
            Logger.info('Pre-filled password: ******');
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Set manual loading state to true
        setState(() {
          _isManualLoading = true;
        });

        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Debug log for initial loading state
        Logger.info('Initial loading state: ${authProvider.isLoading}');

        // Set remember me preference
        Logger.info('Setting Remember Me preference to: $_rememberMe');
        await authProvider.setRememberMe(_rememberMe);

        // Attempt login
        final email = _emailController.text.trim();
        final password = _passwordController.text;
        Logger.info(
            'Attempting login for email: $email with Remember Me: $_rememberMe');

        // The login method in AuthProvider already handles the loading state
        // through runWithLoadingAndErrorHandling
        final success = await authProvider.login(email, password);
        // final success = true;
        
        // Reset WebHomeProvider to home screen and clear conversation state
        final webHomeProvider = Provider.of<WebHomeProvider>(context, listen: false);
        webHomeProvider.currentScreenIndex = ScreenConstants.home;
        await webHomeProvider.resetConversation();
        
        // Also clear ResponsiveStateProvider states
        final responsiveStateProvider = Provider.of<ResponsiveStateProvider>(context, listen: false);
        responsiveStateProvider.clearAllContentStates();
        responsiveStateProvider.setContentState('home', ScreenConstants.home);

        // Debug log for loading state after login
        Logger.info('Loading state after login: ${authProvider.isLoading}');

        // Set manual loading state to false
        // if (mounted) {
        setState(() {
          _isManualLoading = false;
        });
        // }

        // Check if login was successful
        if (success) {
          Logger.info('Login successful, navigating to home screen');

          // // Mark this user as having logged in for greeting purposes
          // final userId = authProvider.user?.id ?? 'default_user';
          // await GreetingHelper.markUserLogin(userId);
          // Logger.info('Marked user login for greeting system: $userId');

          // Use NavigationService to navigate to home screen
          Future.microtask(() {
            NavigationService.navigateToHome();
            Logger.info('Navigation completed using NavigationService');
          });
        } else {
          Logger.info('Login failed');

          // Show error dialog
          if (authProvider.error != null) {
            _showErrorDialog(authProvider.error!);
          } else {
            _showErrorDialog(
                'Login failed. Please check your credentials and try again.');
          }
        }
      } catch (e) {
        // Make sure to reset manual loading state in case of error
        // if (mounted) {
        setState(() {
          _isManualLoading = false;
        });
        // }

        Logger.error('Error during login process: $e');
        // Show error dialog for exceptions
        _showErrorDialog('An error occurred during login. Please try again.');
      } finally {
        // Make sure loading state is reset
        if (_isManualLoading) {
          setState(() {
            _isManualLoading = false;
          });
        }
      }
    } else {
      Logger.info('Form validation failed');
    }
  }

  // Show error dialog
  void _showErrorDialog(String errorMessage) {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: 'common.error',
          content: errorMessage,
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'ok',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          // secondaryButtonText: 'Cancel',
          // onSecondaryPressed: () => Navigator.of(context).pop(),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // Debug log to check loading state
        Logger.info('AuthProvider loading state: ${authProvider.isLoading}');
        return NSLKnowledgeLoaderWrapper(
          //  circularProgressColor: Theme.of(context).primaryColor,
          isLoading: authProvider.isLoading || _isManualLoading,
          child: Scaffold(
            body: Stack(
              children: [
                Row(
                  children: [
                    // Left panel with background image and branding
                    Expanded(
                      flex: 5,
                      child: Container(
                        decoration: BoxDecoration(
                            // color: Theme.of(context).colorScheme.primary,
                            // color: Colors.white,
                            // Use a gradient as fallback if image is not available
                            // gradient: LinearGradient(
                            //   begin: Alignment.topLeft,
                            //   end: Alignment.bottomRight,
                            //   colors: [
                            //     Theme.of(context).colorScheme.primary,
                            //     Theme.of(context).colorScheme.primary.withAlpha(178),
                            //   ],
                            // ),
                            ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Logo or App Name
                              // Text(
                              //   'NSL',
                              //   style: Theme.of(context)
                              //       .textTheme
                              //       .displayLarge
                              //       ?.copyWith(
                              //         color: Theme.of(context).colorScheme.onPrimary,
                              //         fontWeight: FontWeight.bold,
                              //         fontSize: 72,
                              //       ),
                              // ),

                              SvgPicture.asset(
                                'assets/images/login_logo_new.svg',
                              ),
                              // width: 200,height: 200,),
                              const SizedBox(height: AppSpacing.xl),
                              SvgPicture.asset(
                                'assets/images/nsl_login_illustrations.svg',
                              ),
                              // Image(
                              //   image: AssetImage('assets/images/logo.png'),
                              //   height: 200,
                              // ),
                              const SizedBox(height: AppSpacing.xl),
                              Text(
                                'Welcome to the,',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.headlineSmall(
                                      context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                ),
                                // style: Theme.of(context)
                                //     .textTheme
                                //     .displayLarge
                                //     ?.copyWith(
                                //         color: AppColors.black,
                                //         fontWeight: FontWeight.bold,
                                //         fontSize: 22),
                              ),
                              const SizedBox(height: AppSpacing.xxs),
                              Text(
                                textAlign: TextAlign.center,
                                'World’s First Solution Generative Model',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.headlineSmall(
                                      context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.textBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                                // style: Theme.of(context)
                                //     .textTheme
                                //     .displayLarge
                                //     ?.copyWith(
                                //         color: AppColors.textBlue,
                                //         fontWeight: FontWeight.bold,
                                //         fontSize: 22),
                              ),

                              // const SizedBox(height: AppSpacing.xl),
                              // Text(
                              //   'Patented Across Us, Europe, Africa, Asia, Australia \nSolutions Is Natural Language - Without Writing Or Question A Single Line Of Code \nAsk Me About Nsl Invention & Constructs.',
                              //   textAlign: TextAlign.center,
                              //   style: Theme.of(context)
                              //       .textTheme
                              //       .titleMedium
                              //       ?.copyWith(
                              //           color: AppColors.black,
                              //           fontWeight: FontWeight.bold),
                              // ),
                              // Container(
                              //   width: 400,
                              //   padding: const EdgeInsets.all(AppSpacing.lg),
                              //   decoration: BoxDecoration(
                              //     color: Theme.of(context)
                              //         .colorScheme
                              //         .onPrimary
                              //         .withAlpha(25),
                              //     borderRadius:
                              //         BorderRadius.circular(AppTheme.borderRadiusL),
                              //   ),
                              //   child: Column(
                              //     children: [
                              //       Text(
                              //         'Welcome to NSL Platform',
                              //         style: Theme.of(context)
                              //             .textTheme
                              //             .titleLarge
                              //             ?.copyWith(
                              //               color: Theme.of(context)
                              //                   .colorScheme
                              //                   .onPrimary,
                              //               fontWeight: FontWeight.bold,
                              //             ),
                              //       ),
                              //       const SizedBox(height: AppSpacing.md),
                              //       Text(
                              //         'Sign in to access your account and start creating solutions, managing transactions, and more.',
                              //         style: Theme.of(context)
                              //             .textTheme
                              //             .bodyMedium
                              //             ?.copyWith(
                              //               color: Theme.of(context)
                              //                   .colorScheme
                              //                   .onPrimary
                              //                   .withAlpha(230),
                              //             ),
                              //         textAlign: TextAlign.center,
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Right panel with login form
                    Expanded(
                      flex: 5,
                      child: Container(
                        // color: Theme.of(context).colorScheme.surface,
                        color: AppColors.greyBg,
                        // color: Colors.white,
                        child: Stack(
                          children: [
                            Center(
                              child: SingleChildScrollView(
                                padding:
                                    const EdgeInsets.all(AppTheme.spacingL),
                                child: ConstrainedBox(
                                  constraints:
                                      const BoxConstraints(maxWidth: 500),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      //Title Text
                                      Text(
                                        // context.tr('auth.welcomeBack')
                                        context.tr('Login'),
                                        style: FontManager.getCustomStyle(
                                          fontSize: ResponsiveFontSizes
                                              .headlineMedium(context),
                                          fontWeight: FontManager.bold,
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                          color: AppColors.textBlue2,
                                        ),
                                        textAlign: TextAlign.left,
                                      ),
                                      const SizedBox(
                                          height: AppTheme.spacingXxl),

                                      // Subtitle Text
                                      // Text(
                                      //   'Please sign in to continue',
                                      //   style: Theme.of(context)
                                      //       .textTheme
                                      //       .bodyLarge
                                      //       ?.copyWith(
                                      //         color: Theme.of(context)
                                      //             .colorScheme
                                      //             .onSurface
                                      //             .withAlpha(178),
                                      //       ),
                                      //   textAlign: TextAlign.center,
                                      // ),
                                      //  const SizedBox(height: AppTheme.spacingXl),

                                      // Error Message (if any)
                                      // if (authProvider.error != null) ...[
                                      //   Container(
                                      //     padding:
                                      //         const EdgeInsets.all(AppTheme.spacingM),
                                      //     decoration: BoxDecoration(
                                      //       color: AppTheme.errorColor.withAlpha(25),
                                      //       borderRadius: BorderRadius.circular(
                                      //           AppTheme.borderRadiusM),
                                      //     ),
                                      //     child: Text(
                                      //       authProvider.error!,
                                      //       style: Theme.of(context)
                                      //           .textTheme
                                      //           .bodyMedium
                                      //           ?.copyWith(
                                      //             color: AppTheme.errorColor,
                                      //           ),
                                      //       textAlign: TextAlign.center,
                                      //     ),
                                      //   ),
                                      //   const SizedBox(height: AppTheme.spacingM),
                                      // ],

                                      // Login Form
                                      Row(
                                        children: [
                                          Expanded(
                                              flex: 12,
                                              child: _buildLoginForm(
                                                  context, authProvider)),
                                          Expanded(flex: 1, child: SizedBox())
                                        ],
                                      ),

                                      //   const SizedBox(height: AppTheme.spacingXl),

                                      // Footer
                                      // Text(
                                      //   '© ${DateTime.now().year} NSL Platform. All rights reserved.',
                                      //   style: Theme.of(context).textTheme.bodySmall,
                                      //   textAlign: TextAlign.center,
                                      // ),
                                    ],
                                  ),
                                ),
                              ),
                            ),

                            // No loading overlay here anymore - using full-screen overlay instead
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                // Full-screen loading overlay
                // if (authProvider.isLoading || _isManualLoading)
                //   LoadingOverlay(
                //     text: context.tr('common.loading'),
                //     color: AppColors.textBlue2,
                //     indicatorSize: 30,
                //     fontSize: 16,
                //   ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginForm(BuildContext context, AuthProvider authProvider) {
    return Container(
      // color: Colors.white,
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Email Field
            // const SizedBox(height: AppTheme.spacingXl),
            ValidatedTextField(
              controller: _emailController,
              label: context.tr('auth.username'),
              placeholder: 'Enter your username',
              type: AppTextFieldType.email,
              textInputAction: TextInputAction.next,
              // prefix: SizedBox(),
              //  const Icon(Icons.email_outlined),
              enabled: !(authProvider.isLoading || _isManualLoading),
              validator: Validators.validateUsername,
              noBorder: true,
            ),
            const SizedBox(height: AppTheme.spacingM),

            // Password Field
            PasswordField(
              controller: _passwordController,
              label: context.tr('auth.password'),
              placeholder: 'Enter your password',
              textInputAction: TextInputAction.done,
              enabled: !(authProvider.isLoading || _isManualLoading),
              validator: Validators.validatePassword,
              noBorder: true,
              prefix: SizedBox(),
            ),
            const SizedBox(height: AppTheme.spacingS),

            // Role Field
            // RoleField(
            //   label: 'Role',
            //   placeholder: 'Select your role',
            //   enabled: !(authProvider.isLoading || _isManualLoading),
            //   noBorder: true,
            //   roles: const ['Admin', 'User', 'Manager', 'Guest'],
            //   onChanged: (value) {
            //     // Handle role selection
            //     Logger.info('Selected role: $value');
            //   },
            // ),
            // const SizedBox(height: AppTheme.spacingS),

            // Remember Me and Forgot Password
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            // Remember Me Checkbox
            // Row(
            //   children: [
            //     Checkbox(
            //       value: _rememberMe,
            //       onChanged: (authProvider.isLoading || _isManualLoading)
            //           ? null
            //           : (value) {
            //               setState(() {
            //                 _rememberMe = value ?? false;
            //               });
            //             },
            //     ),
            //     Text(
            //       context.tr('auth.rememberMe'),
            //       style: Theme.of(context)
            //           .textTheme
            //           .bodyMedium
            //           ?.copyWith(color: AppColors.black),
            //     ),
            //   ],
            // ),

            // Forgot Password Button
            // TextButton(
            //   onPressed: (authProvider.isLoading || _isManualLoading)
            //       ? null
            //       : () {
            //           // TODO: Implement forgot password
            //           Logger.info('Forgot password tapped');
            //         },
            //   child: Text(
            //     context.tr('auth.forgotPassword'),
            //     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            //           // color: Theme.of(context).colorScheme.primary,
            //           color: AppColors.textBlue2,
            //           fontWeight: FontWeight.w600,
            //         ),
            //   ),
            // ),
            //   ],
            // ),
            const SizedBox(height: AppTheme.spacingM),

            // Login Button
            Center(
              child: SizedBox(
                //height: 42.0,
                width: 100,
                child: AuthButton(
                  text: context.tr(
                    'auth.login',
                  ),
                  onPressed: (authProvider.isLoading || _isManualLoading)
                      ? null
                      : _login,
                  isLoading: authProvider.isLoading || _isManualLoading,
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),

            // Register Button

            // padding: const EdgeInsets.only(left: 80.0),
            AuthLink(
              text: "${context.tr('auth.notRegistered')} ",
              linkText: context.tr('auth.signUp'),
              onPressed: (authProvider.isLoading || _isManualLoading)
                  ? null
                  : () {
                      NavigationService.push(const ResponsiveRegisterBuilder());
                      Logger.info('Register button tapped');
                    },
              isDisabled: authProvider.isLoading || _isManualLoading,
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration customInputDecoration = InputDecoration(
    fillColor: AppColors.white,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
      borderSide: const BorderSide(color: AppTheme.primaryColor),
    ),
    enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        borderSide: BorderSide.none),
    focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        borderSide: BorderSide.none),
    errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        borderSide: BorderSide.none),
    focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
        borderSide: BorderSide.none),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusM),
      borderSide: BorderSide.none,
    ),
  );
}
