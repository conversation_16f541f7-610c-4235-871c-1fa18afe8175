import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable vertical slider widget.
///
/// This widget provides a vertical slider with various customization options
/// including min/max values, divisions, colors, labels, and more.
class VerticalSliderWidget extends StatefulWidget {
  /// Minimum value for the slider
  final double min;

  /// Maximum value for the slider
  final double max;

  /// Current value of the slider
  final double initial;

  /// Number of discrete divisions for the slider
  final int? divisions;

  /// Color of the active portion of the slider
  final Color activeColor;

  /// Color of the inactive portion of the slider
  final Color? inactiveColor;

  /// Color of the slider thumb
  final Color? thumbColor;

  /// Height of the slider track
  final double? trackHeight;

  /// Style for the slider label
  final TextStyle? labelStyle;

  /// Whether to show the value label
  final bool showLabel;

  /// Custom label format function
  final String Function(double)? labelFormat;

  /// Height of the slider (width in standard orientation)
  final double height;

  /// Width of the slider (height in standard orientation)
  final double width;

  /// Callback when the slider value changes
  final ValueChanged<double>? onChanged;

  /// Callback when the slider value change ends
  final ValueChanged<double>? onChangeEnd;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  const VerticalSliderWidget({
    super.key,
    this.min = 0.0,
    this.max = 100.0,
    this.initial = 0.0,
    this.divisions,
    this.activeColor = Colors.blue,
    this.inactiveColor,
    this.thumbColor,
    this.trackHeight,
    this.labelStyle,
    this.showLabel = true,
    this.labelFormat,
    this.height = 150.0,
    this.width = 60.0,
    this.onChanged,
    this.onChangeEnd,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
  });

  /// Creates a VerticalSliderWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the VerticalSliderWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "min": 0,
  ///   "max": 100,
  ///   "initial": 50,
  ///   "divisions": 5,
  ///   "activeColor": "blue",
  ///   "height": 300,
  ///   "showLabel": true
  /// }
  /// ```
  factory VerticalSliderWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          case 'teal':
            return Colors.teal;
          case 'cyan':
            return Colors.cyan;
          case 'amber':
            return Colors.amber;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final fontSize = (styleValue['fontSize'] as num?)?.toDouble();
        final isBold = styleValue['bold'] as bool? ?? false;
        final isItalic = styleValue['italic'] as bool? ?? false;
        final color = parseColor(styleValue['color']);

        return TextStyle(
          fontSize: fontSize,
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
          color: color,
        );
      }

      return null;
    }

    // Parse label format function
    String Function(double)? parseLabelFormat(dynamic formatValue) {
      if (formatValue == null) return null;

      if (formatValue is String) {
        switch (formatValue.toLowerCase()) {
          case 'integer':
          case 'int':
            return (value) => value.toStringAsFixed(0);
          case 'decimal':
          case 'float':
            return (value) => value.toStringAsFixed(1);
          case 'precise':
            return (value) => value.toStringAsFixed(2);
          case 'percentage':
          case 'percent':
            return (value) => '${value.toStringAsFixed(0)}%';
          case 'currency':
            return (value) => '\$${value.toStringAsFixed(2)}';
          default:
            return null;
        }
      }

      return null;
    }

    return VerticalSliderWidget(
      min: (json['min'] as num?)?.toDouble() ?? 0.0,
      max: (json['max'] as num?)?.toDouble() ?? 100.0,
      initial: (json['initial'] as num?)?.toDouble() ?? 0.0,
      divisions: json['divisions'] as int?,
      activeColor: parseColor(json['activeColor']) ?? Colors.blue,
      inactiveColor: parseColor(json['inactiveColor']),
      thumbColor: parseColor(json['thumbColor']),
      trackHeight: (json['trackHeight'] as num?)?.toDouble(),
      labelStyle: parseTextStyle(json['labelStyle']),
      showLabel: json['showLabel'] as bool? ?? true,
      labelFormat: parseLabelFormat(json['labelFormat']),
      height: (json['height'] as num?)?.toDouble() ?? 150.0,
      width: (json['width'] as num?)?.toDouble() ?? 60.0,
      onChanged:
          json['onChanged'] == true
              ? (value) {
                debugPrint('Vertical slider value changed: $value');
              }
              : null,
      onChangeEnd:
          json['onChangeEnd'] == true
              ? (value) {
                debugPrint('Vertical slider value change ended: $value');
              }
              : null,
      onHover:
          json['onHover'] == true
              ? (isHovered) {
                debugPrint('Vertical slider hover: $isHovered');
              }
              : null,
      onFocus:
          json['onFocus'] == true
              ? (isFocused) {
                debugPrint('Vertical slider focus: $isFocused');
              }
              : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
    );
  }

  /// Converts the VerticalSliderWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'min': min,
      'max': max,
      'initial': initial,
      'showLabel': showLabel,
      'height': height,
      'width': width,
      'activeColor': _colorToString(activeColor),
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (divisions != null) json['divisions'] = divisions;
    if (inactiveColor != null)
      json['inactiveColor'] = _colorToString(inactiveColor!);
    if (thumbColor != null) json['thumbColor'] = _colorToString(thumbColor!);
    if (trackHeight != null) json['trackHeight'] = trackHeight;
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add label style if present
    if (labelStyle != null) {
      json['labelStyle'] = {
        'fontSize': labelStyle!.fontSize,
        'bold': labelStyle!.fontWeight == FontWeight.bold,
        'italic': labelStyle!.fontStyle == FontStyle.italic,
        if (labelStyle!.color != null)
          'color': _colorToString(labelStyle!.color!),
      };
    }

    // Add callback flags
    if (onChanged != null) json['onChanged'] = true;
    if (onChangeEnd != null) json['onChangeEnd'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  @override
  State<VerticalSliderWidget> createState() => _VerticalSliderWidgetState();
}

class _VerticalSliderWidgetState extends State<VerticalSliderWidget> {
  late double _currentValue;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initial.clamp(widget.min, widget.max);

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    super.dispose();
  }

  String _formatLabel(double value) {
    if (widget.labelFormat != null) {
      return widget.labelFormat!(value);
    }

    // Default formatting based on value range
    if ((widget.max - widget.min) < 1) {
      // For small ranges, show more decimal places
      return value.toStringAsFixed(2);
    } else if ((widget.max - widget.min) < 10) {
      // For medium ranges, show one decimal place
      return value.toStringAsFixed(1);
    } else {
      // For large ranges, show no decimal places
      return value.toStringAsFixed(0);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Remove extra space by using the minimum width needed for the slider thumb
    Widget sliderWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: Focus(
        focusNode: _focusNode,
        child: SizedBox(
          height: widget.height,
          width: 32, // Reduce width to fit the thumb and border only
          child: RotatedBox(
            quarterTurns: 3, // Rotate to make horizontal slider vertical
            child: SliderTheme(
              data: SliderThemeData(
                trackHeight: widget.trackHeight ?? 8.0,
                activeTrackColor:
                    _isHovered && widget.hoverColor != null
                        ? widget.hoverColor!
                        : _isFocused && widget.focusColor != null
                        ? widget.focusColor!
                        : Color(0xFF0058FF),
                inactiveTrackColor:
                    widget.inactiveColor ?? Colors.grey.shade300,
                thumbColor:
                    Colors.transparent, // Make transparent since custom thumb handles color
                overlayColor:
                    Colors.transparent, // Remove overlay since custom thumb handles scaling
                thumbShape: _CustomVerticalSliderThumbCircle(
                  thumbRadius: 12.0,
                  thumbColor: widget.thumbColor ?? Color(0xFF0058FF),
                  min: widget.min,
                  max: widget.max,
                  textStyle: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    fontSize: 12,
                  ),
                ),
                overlayShape: SliderComponentShape.noOverlay,
                showValueIndicator: ShowValueIndicator.never,
              ),
              child: Slider(
                value: _currentValue,
                min: widget.min,
                max: widget.max,
                divisions: widget.divisions,
                onChanged: (value) {
                  setState(() {
                    _currentValue = value;
                  });

                  if (widget.enableFeedback) {
                    HapticFeedback.selectionClick();
                  }

                  if (widget.onChanged != null) {
                    widget.onChanged!(value);
                  }
                },
                onChangeEnd: widget.onChangeEnd,
              ),
            ),
          ),
        ),
      ),
    );

    // Add tooltip if needed
    // if (widget.tooltip != null) {
    //   sliderWidget = Tooltip(
    //     message: widget.tooltip!,
    //     child: sliderWidget,
    //   );
    // }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      sliderWidget = Semantics(
        label: widget.semanticsLabel,
        value: _formatLabel(_currentValue),
        slider: true,
        child: sliderWidget,
      );
    }

    return sliderWidget;
  }
}

/// Custom vertical slider thumb shape that displays the value inside the thumb
class _CustomVerticalSliderThumbCircle extends SliderComponentShape {
  final double thumbRadius;
  final Color thumbColor;
  final double min;
  final double max;
  final TextStyle textStyle;

  _CustomVerticalSliderThumbCircle({
    required this.thumbRadius,
    required this.thumbColor,
    required this.min,
    required this.max,
    required this.textStyle,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;

    // Simple scaling based on activation animation
    final double scale = 1.0 + (activationAnimation.value * 0.2);
    final double currentRadius = thumbRadius * scale;

    // Draw white background circle
    final backgroundPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, backgroundPaint);

    // Draw blue border
    final borderPaint =
        Paint()
          ..color = thumbColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
    canvas.drawCircle(center, currentRadius - 1.0, borderPaint);

    // Calculate the actual value from the slider position
    final actualValue = min + (value * (max - min));

    // Format the value as integer (no decimal points)
    final valueText = actualValue.round().toString();

    // Draw text (value inside thumb) with blue color to match border
    final textSpan = TextSpan(
      text: valueText,
      style: textStyle.copyWith(
        color: thumbColor,
        fontSize: textStyle.fontSize! * scale,
      ),
    );

    final tp = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: textDirection,
    );

    tp.layout();

    // Save the current canvas state
    canvas.save();

    // Translate to the center point
    canvas.translate(center.dx, center.dy);

    // Counter-rotate the text by 90 degrees (1 quarter turn) to make it horizontal
    // Since the slider is rotated 270 degrees, we need to rotate text 90 degrees back
    canvas.rotate(1.5708); // 90 degrees in radians (π/2)

    // Paint the text at the origin (which is now the center due to translation)
    tp.paint(canvas, Offset(-tp.width / 2, -tp.height / 2));

    // Restore the canvas state
    canvas.restore();
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}
