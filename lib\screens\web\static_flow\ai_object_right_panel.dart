import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/ai_validation_popup.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:provider/provider.dart';

/// Model class for validation rules
class ValidationRule {
  final String title;
  final String description;
  final String category;
  final String complexity;

  const ValidationRule({
    required this.title,
    required this.description,
    required this.category,
    required this.complexity,
  });
}

/// Model class for industry bundles
class IndustryBundle {
  final String title;
  final String match;
  final String modules;
  final String description;

  const IndustryBundle({
    required this.title,
    required this.match,
    required this.modules,
    required this.description,
  });
}

/// Enum for complexity levels
enum ComplexityLevel {
  simple,
  moderate,
  complex;

  String get displayName {
    switch (this) {
      case ComplexityLevel.simple:
        return 'SIMPLE';
      case ComplexityLevel.moderate:
        return 'MODERATE';
      case ComplexityLevel.complex:
        return 'COMPLEX';
    }
  }

  Color get backgroundColor {
    switch (this) {
      case ComplexityLevel.simple:
        return const Color(0xFFE8F5E8);
      case ComplexityLevel.moderate:
        return const Color(0xFFFFF4E6);
      case ComplexityLevel.complex:
        return const Color(0xFFFFE6E6);
    }
  }
}

class AiObjectRightPanel extends StatefulWidget {
  const AiObjectRightPanel({super.key});

  @override
  State<AiObjectRightPanel> createState() => _AiObjectRightPanelState();
}

class _AiObjectRightPanelState extends State<AiObjectRightPanel> {
  int _currentPage = 1;
  int _itemsPerPage = 5;
  int _currentBundlePage = 1;
  int _bundleItemsPerPage = 2;

  // State for expandable sections
  bool _isValidationRulesExpanded = true;
  bool _isIndustryBundlesExpanded = true;

  // UI Constants using AppSpacing
  static const double _validationItemHeight = 45.0;
  static const double _headerHeight = 30.0;
  static const double _paginationHeight = 25.0;

  // Sample data for validation rules
  static const List<ValidationRule> _validationRules = [
    ValidationRule(
      title: 'Email Validation Rules',
      description: 'Add REGEX EMAIL and IS_UNIQUE operators for email field',
      category: 'Business Rules',
      complexity: 'SIMPLE',
    ),
    ValidationRule(
      title: 'Phone Format Validation',
      description:
          'Apply MATCHES_PATTERN operator for international phone numbers',
      category: 'Business Rules',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Customer-Address Relationship',
      description:
          'Configure one-to-many with CASCADE delete for address cleanup',
      category: 'Entity Relationship',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Title of the Issue',
      description: 'Description of the Issue',
      category: 'Business Rules',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Address Auto-Complete',
      description: 'Implement auto-complete for the field',
      category: 'Entity Relationship',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Data Validation Rules',
      description: 'Additional validation for data integrity',
      category: 'Business Rules',
      complexity: 'SIMPLE',
    ),
    ValidationRule(
      title: 'Security Validation',
      description: 'Implement security checks for sensitive data',
      category: 'Security',
      complexity: 'COMPLEX',
    ),
    // Duplicate entries for demonstration
    ValidationRule(
      title: 'Email Validation Rules',
      description: 'Add REGEX EMAIL and IS_UNIQUE operators for email field',
      category: 'Business Rules',
      complexity: 'SIMPLE',
    ),
    ValidationRule(
      title: 'Phone Format Validation',
      description:
          'Apply MATCHES_PATTERN operator for international phone numbers',
      category: 'Business Rules',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Customer-Address Relationship',
      description:
          'Configure one-to-many with CASCADE delete for address cleanup',
      category: 'Entity Relationship',
      complexity: 'MODERATE',
    ),
    ValidationRule(
      title: 'Data Validation Rules',
      description: 'Additional validation for data integrity',
      category: 'Business Rules',
      complexity: 'SIMPLE',
    ),
    ValidationRule(
      title: 'Security Validation',
      description: 'Implement security checks for sensitive data',
      category: 'Security',
      complexity: 'COMPLEX',
    ),
  ];

  // Sample data for industry bundles
  static const List<IndustryBundle> _industryBundles = [
    IndustryBundle(
      title: 'E-Commerce Complete',
      match: '92% match',
      modules: '23 resolutions',
      description:
          'Full e-commerce customer entity with privacy, UX, and business intelligence',
    ),
    IndustryBundle(
      title: 'Security First',
      match: '92% match',
      modules: '23 resolutions',
      description: 'Enhanced security and compliance for sensitive data',
    ),
    IndustryBundle(
      title: 'Healthcare Bundle',
      match: '88% match',
      modules: '18 resolutions',
      description:
          'HIPAA compliant healthcare data management with patient privacy controls',
    ),
    IndustryBundle(
      title: 'Financial Services',
      match: '95% match',
      modules: '31 resolutions',
      description:
          'Banking and financial compliance with fraud detection and audit trails',
    ),
  ];

  // Computed properties
  int get _totalPages => (_validationRules.length / _itemsPerPage).ceil();
  int get _totalBundlePages =>
      (_industryBundles.length / _bundleItemsPerPage).ceil();

  List<ValidationRule> get _currentPageItems {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _validationRules.length);
    return _validationRules.sublist(startIndex, endIndex);
  }

  List<IndustryBundle> get _currentBundlePageItems {
    final startIndex = (_currentBundlePage - 1) * _bundleItemsPerPage;
    final endIndex =
        (startIndex + _bundleItemsPerPage).clamp(0, _industryBundles.length);
    return _industryBundles.sublist(startIndex, endIndex);
  }

  // Calculate dynamic items per page for validation rules based on available height
  int _calculateValidationItemsPerPage(double availableHeight) {
    final contentHeight =
        availableHeight - _headerHeight - _paginationHeight - AppSpacing.xs;
    final maxItems = (contentHeight / _validationItemHeight).floor();
    return maxItems > 0 ? maxItems : 1; // Ensure at least 1 item is shown
  }

  // Calculate items per page for industry bundles based on screen width
  int _calculateBundleItemsPerPage(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1920) {
      return 2; // Exactly 2 items for 1920px and above
    } else if (screenWidth >= 1366) {
      return 1; // Exactly 1 item for 1366px and above
    } else {
      return 1; // Default to 1 item for smaller screens
    }
  }

  // Get complexity color based on complexity level
  Color _getComplexityColor(String complexity) {
    switch (complexity.toUpperCase()) {
      case 'SIMPLE':
        return const Color(0xFFE8F5E8);
      case 'MODERATE':
        return const Color(0xFFFFF4E6);
      case 'COMPLEX':
        return const Color(0xFFFFE6E6);
      default:
        return const Color(0xFFF5F5F5);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          color: Colors.transparent,
          child: ScrollConfiguration(
            behavior: ScrollConfiguration.of(context).copyWith(
              scrollbars: false,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                // Section 1: Validation Rules with Header + Body (takes full available height)
                Expanded(
                    child: IgnorePointer(
                      ignoring: Provider.of<WebHomeProviderStatic>(context).isAIMode,
                      child: Opacity(
                        opacity: Provider.of<WebHomeProviderStatic>(context).isAIMode ? 0.5 : 1.0,
                        child: SizedBox(
                          width: constraints.maxWidth,
                          child: _buildValidationRulesSection(),
                        ),
                      ),
                    ),
                  ),

                SizedBox(height: AppSpacing.xs),
                // Section 2: Industry Bundles with Header + Body (natural size)
                SizedBox(
                  width: constraints.maxWidth,
                  child: _buildIndustryBundlesSection(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Reusable header widget for both sections
  Widget _buildSectionHeader({
    required BuildContext context,
    required String title,
    IconData? icon,
    required List<Color> gradientColors,
    Widget? actionButton,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: gradientColors),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: Colors.white,
              size: AppSpacing.md,
            ),
            SizedBox(width: AppSpacing.xxs),
          ],
          Expanded(
            child: Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontManager.semiBold,
                color: Colors.white,
                fontFamily: FontManager.fontFamilyTiemposText,
                height: 1.2,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (actionButton != null) actionButton,
        ],
      ),
    );
  }

  Widget _buildValidationRulesSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dynamic items per page based on available height
        final dynamicItemsPerPage =
            _calculateValidationItemsPerPage(constraints.maxHeight);

        // Update itemsPerPage if it's different from current value
        if (_itemsPerPage != dynamicItemsPerPage) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _itemsPerPage = dynamicItemsPerPage;
              // Reset to first page if current page is now out of bounds
              if (_currentPage > _totalPages) {
                _currentPage = 1;
              }
            });
          });
        }

        return Container(
          width: constraints.maxWidth,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              // Section 1 Header
              _buildSectionHeader(
                context: context,
                title: 'AI Smart Resolution (${_validationRules.length})',
                icon: Icons.auto_awesome,
                gradientColors: const [Color(0xff0058FF), Color(0xff0B3A91)],
                actionButton: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.xs,
                        vertical: AppSpacing.xxs,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppSpacing.xxs),
                      ),
                      child: Text(
                        'BULK APPLY',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontManager.semiBold,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          height: 1.2,
                        ),
                      ),
                    ),
                    SizedBox(width: AppSpacing.xs),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isValidationRulesExpanded = !_isValidationRulesExpanded;
                        });
                      },
                      child: Icon(
                        _isValidationRulesExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: AppSpacing.md,
                      ),
                    ),
                  ],
                ),
              ),
              // Section 1 Body (Dynamic Height) - Collapsible
              if (_isValidationRulesExpanded)
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                      // Expanded SingleChildScrollView for rule items to prevent overflow
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(
                            AppSpacing.xs,
                            AppSpacing.xs,
                            AppSpacing.xs,
                            0,
                          ),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: _currentPageItems
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                final index = entry.key;
                                final item = entry.value;
                                return Padding(
                                  padding: EdgeInsets.only(
                                    bottom: index < _currentPageItems.length - 1
                                        ? AppSpacing.xxs
                                        : 0,
                                  ),
                                  child: _buildValidationRuleItem(item),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ),
                      // Pagination at bottom right
                      if (_totalPages > 1)
                        Container(
                          padding: EdgeInsets.fromLTRB(
                            AppSpacing.xs,
                            AppSpacing.xxs,
                            AppSpacing.xs,
                            AppSpacing.xxs,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              _buildPaginationControls(),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildValidationRuleItem(ValidationRule rule) {
    return InkWell(
      onTap: () {
        // Show AiValidationPopup when clicked
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (context) => const AiValidationPopup(),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.xs,
          vertical: AppSpacing.xxs + 1,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: const BorderSide(
              color: Colors.blue,
              width: 2,
            ),
            top: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            right: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
            bottom: BorderSide(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // First row - Title and Category
                      Padding(
                        padding: EdgeInsets.only(top: AppSpacing.xxs / 2),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Tooltip(
                                message: rule.title,
                                child: Text(
                                  rule.title,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.labelSmall(context),
                                    fontWeight: FontManager.bold,
                                    color: Colors.black,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    height: 1.2,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ),
                            SizedBox(width: AppSpacing.xxs),
                            Text(
                              rule.category,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontManager.regular,
                                color: const Color(0xFF6B7280),
                                fontFamily: FontManager.fontFamilyTiemposText,
                                height: 1.2,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: AppSpacing.size6),
                      // Second row - Description
                      Tooltip(
                        message: rule.description,
                        child: Text(
                          rule.description,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontManager.regular,
                            color: const Color(0xFF6B7280),
                            fontFamily: FontManager.fontFamilyTiemposText,
                            height: 1.2,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: AppSpacing.size6),
                // Complexity badge on the right side
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs,
                    vertical: AppSpacing.xxs,
                  ),
                  decoration: BoxDecoration(
                    color: _getComplexityColor(rule.complexity),
                  ),
                  child: Text(
                    rule.complexity,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndustryBundlesSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dynamic items per page for industry bundles
        final dynamicBundleItemsPerPage = _calculateBundleItemsPerPage(context);

        // Update bundleItemsPerPage if it's different from current value
        if (_bundleItemsPerPage != dynamicBundleItemsPerPage) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _bundleItemsPerPage = dynamicBundleItemsPerPage;
              // Reset to first page if current page is now out of bounds
              if (_currentBundlePage > _totalBundlePages) {
                _currentBundlePage = 1;
              }
            });
          });
        }

        return Container(
          width: constraints.maxWidth,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Section 2 Header
              _buildSectionHeader(
                context: context,
                title: 'Industry Bundles (${_industryBundles.length})',
                gradientColors: const [Color(0xff835BED), Color(0xff7540E5)],
                actionButton: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isIndustryBundlesExpanded = !_isIndustryBundlesExpanded;
                    });
                  },
                  child: Icon(
                    _isIndustryBundlesExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                    color: Colors.white,
                    size: AppSpacing.md,
                  ),
                ),
              ),
              // Section 2 Body (Natural Height) - Collapsible
              if (_isIndustryBundlesExpanded)
                Container(
                  width: constraints.maxWidth,
                  color: Colors.white,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // Column for bundle items without height constraints
                    Padding(
                      padding: EdgeInsets.fromLTRB(
                        AppSpacing.xs,
                        AppSpacing.xs,
                        AppSpacing.xs,
                        0,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: _currentBundlePageItems
                            .asMap()
                            .entries
                            .map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: index < _currentBundlePageItems.length - 1
                                  ? AppSpacing.xxs
                                  : 0,
                            ),
                            child: _buildIndustryBundleItem(item),
                          );
                        }).toList(),
                      ),
                    ),
                    // Pagination at bottom right
                    if (_totalBundlePages > 1)
                      Container(
                        padding: EdgeInsets.fromLTRB(
                          AppSpacing.xs,
                          AppSpacing.xxs,
                          AppSpacing.xs,
                          AppSpacing.xxs,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _buildBundlePaginationControls(),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIndustryBundleItem(IndustryBundle bundle) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.size6,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: const BorderSide(
            color: Color(0xff7846E7),
            width: 2,
          ),
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 0.5,
          ),
          right: BorderSide(
            color: Colors.grey.shade300,
            width: 0.5,
          ),
          bottom: BorderSide(
            color: Colors.grey.shade300,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Tooltip(
                  message: bundle.title,
                  mouseCursor: SystemMouseCursors.click,
                  child: Text(
                    bundle.title,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontWeight: FontManager.semiBold,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      height: 1.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
              Text(
                bundle.match,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.regular,
                  color: const Color(0xFF6B7280),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  height: 1.2,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.xxs + 1),
          Text(
            bundle.modules,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelSmall(context),
              fontWeight: FontManager.regular,
              color: const Color(0xFF6B7280),
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.2,
            ),
          ),
          SizedBox(height: AppSpacing.size6),
          Tooltip(
            message: bundle.description,
            mouseCursor: SystemMouseCursors.click,
            child: Text(
              bundle.description,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontManager.regular,
                color: const Color(0xFF6B7280),
                fontFamily: FontManager.fontFamilyTiemposText,
                height: 1.2,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          SizedBox(height: AppSpacing.xxs),
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.size6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF0058FF),
                  borderRadius: BorderRadius.circular(AppSpacing.xxs),
                ),
                child: Text(
                  'APPLY ALL',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontManager.regular,
                    color: Colors.white,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    height: 1.2,
                  ),
                ),
              ),
              SizedBox(width: AppSpacing.xs),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.size6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
                  borderRadius: BorderRadius.circular(AppSpacing.xxs),
                ),
                child: Text(
                  'VIEW',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontManager.regular,
                    color: const Color(0xFF6B7280),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    height: 1.2,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Previous button
        InkWell(
          onTap: _currentPage > 1
              ? () {
                  setState(() {
                    _currentPage--;
                  });
                }
              : null,
          child: SizedBox(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            child: Icon(
              Icons.chevron_left,
              size: AppSpacing.lg,
              color: Colors.grey.shade400,
            ),
          ),
        ),
        // Next button
        InkWell(
          onTap: _currentPage < _totalPages
              ? () {
                  setState(() {
                    _currentPage++;
                  });
                }
              : null,
          child: SizedBox(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            child: Icon(
              Icons.chevron_right,
              size: AppSpacing.lg,
              color: Colors.grey.shade400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBundlePaginationControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Previous button
        InkWell(
          onTap: _currentBundlePage > 1
              ? () {
                  setState(() {
                    _currentBundlePage--;
                  });
                }
              : null,
          child: SizedBox(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            child: Icon(
              Icons.chevron_left,
              size: AppSpacing.lg,
              color: Colors.grey.shade400,
            ),
          ),
        ),
        // Next button
        InkWell(
          onTap: _currentBundlePage < _totalBundlePages
              ? () {
                  setState(() {
                    _currentBundlePage++;
                  });
                }
              : null,
          child: SizedBox(
            width: AppSpacing.lg,
            height: AppSpacing.lg,
            child: Icon(
              Icons.chevron_right,
              size: AppSpacing.lg,
              color: Colors.grey.shade400,
            ),
          ),
        ),
      ],
    );
  }
}
