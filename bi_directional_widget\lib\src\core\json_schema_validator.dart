import '../exceptions/json_validation_exception.dart';

/// JSON Schema validator for widget configurations
/// 
/// This class provides validation functionality to ensure that JSON configurations
/// conform to the expected schema for widget creation.
class JsonSchemaValidator {
  /// Validates JSON against a schema
  /// 
  /// [json] The JSON to validate
  /// [schema] The schema to validate against
  /// [errors] Optional list to collect validation errors
  /// Returns true if valid, false otherwise
  static bool validate(
    Map<String, dynamic> json,
    Map<String, dynamic> schema, {
    List<String>? errors,
  }) {
    errors?.clear();
    return _validateObject(json, schema, '', errors ?? []);
  }

  /// Validates JSON and throws exception if invalid
  /// 
  /// [json] The JSON to validate
  /// [schema] The schema to validate against
  /// [widgetType] Optional widget type for error context
  /// Throws [JsonValidationException] if validation fails
  static void validateOrThrow(
    Map<String, dynamic> json,
    Map<String, dynamic> schema, {
    String? widgetType,
  }) {
    final errors = <String>[];
    if (!validate(json, schema, errors: errors)) {
      throw JsonValidationException(
        'JSON validation failed',
        errors: errors,
        json: json,
        widgetType: widgetType,
      );
    }
  }

  /// Validates an object against a schema
  static bool _validateObject(
    Map<String, dynamic> json,
    Map<String, dynamic> schema,
    String path,
    List<String> errors,
  ) {
    bool isValid = true;

    // Check type
    final type = schema['type'] as String?;
    if (type == 'object') {
      // Validate properties
      final properties = schema['properties'] as Map<String, dynamic>?;
      if (properties != null) {
        properties.forEach((key, propertySchema) {
          final propertyPath = path.isEmpty ? key : '$path.$key';
          
          if (json.containsKey(key)) {
            if (!_validateValue(json[key], propertySchema as Map<String, dynamic>, propertyPath, errors)) {
              isValid = false;
            }
          }
        });
      }

      // Check required fields
      final required = schema['required'] as List<dynamic>?;
      if (required != null) {
        for (final field in required) {
          if (!json.containsKey(field)) {
            errors.add('Missing required field: ${path.isEmpty ? field : '$path.$field'}');
            isValid = false;
          }
        }
      }
    }

    return isValid;
  }

  /// Validates a value against a schema
  static bool _validateValue(
    dynamic value,
    Map<String, dynamic> schema,
    String path,
    List<String> errors,
  ) {
    bool isValid = true;

    // Handle null values
    if (value == null) {
      final nullable = schema['nullable'] as bool? ?? false;
      if (!nullable) {
        errors.add('Value at $path cannot be null');
        return false;
      }
      return true;
    }

    // Check type
    final type = schema['type'] as String?;
    if (type != null) {
      if (!_validateType(value, type, path, errors)) {
        isValid = false;
      }
    }

    // Check enum values
    final enumValues = schema['enum'] as List<dynamic>?;
    if (enumValues != null) {
      if (!enumValues.contains(value)) {
        errors.add('Value at $path must be one of: ${enumValues.join(', ')}');
        isValid = false;
      }
    }

    // Check const value
    final constValue = schema['const'];
    if (constValue != null && value != constValue) {
      errors.add('Value at $path must be: $constValue');
      isValid = false;
    }

    // Check oneOf
    final oneOf = schema['oneOf'] as List<dynamic>?;
    if (oneOf != null) {
      bool matchesOne = false;
      for (final subSchema in oneOf) {
        final subErrors = <String>[];
        if (_validateValue(value, subSchema as Map<String, dynamic>, path, subErrors)) {
          matchesOne = true;
          break;
        }
      }
      if (!matchesOne) {
        errors.add('Value at $path does not match any of the allowed schemas');
        isValid = false;
      }
    }

    // Type-specific validations
    if (type == 'string') {
      isValid &= _validateString(value, schema, path, errors);
    } else if (type == 'number' || type == 'integer') {
      isValid &= _validateNumber(value, schema, path, errors);
    } else if (type == 'array') {
      isValid &= _validateArray(value, schema, path, errors);
    } else if (type == 'object') {
      if (value is Map<String, dynamic>) {
        isValid &= _validateObject(value, schema, path, errors);
      }
    }

    return isValid;
  }

  /// Validates type
  static bool _validateType(dynamic value, String type, String path, List<String> errors) {
    switch (type) {
      case 'string':
        if (value is! String) {
          errors.add('Value at $path must be a string');
          return false;
        }
        break;
      case 'number':
        if (value is! num) {
          errors.add('Value at $path must be a number');
          return false;
        }
        break;
      case 'integer':
        if (value is! int) {
          errors.add('Value at $path must be an integer');
          return false;
        }
        break;
      case 'boolean':
        if (value is! bool) {
          errors.add('Value at $path must be a boolean');
          return false;
        }
        break;
      case 'array':
        if (value is! List) {
          errors.add('Value at $path must be an array');
          return false;
        }
        break;
      case 'object':
        if (value is! Map) {
          errors.add('Value at $path must be an object');
          return false;
        }
        break;
    }
    return true;
  }

  /// Validates string constraints
  static bool _validateString(dynamic value, Map<String, dynamic> schema, String path, List<String> errors) {
    if (value is! String) return true;

    bool isValid = true;

    // Check minimum length
    final minLength = schema['minLength'] as int?;
    if (minLength != null && value.length < minLength) {
      errors.add('String at $path must be at least $minLength characters long');
      isValid = false;
    }

    // Check maximum length
    final maxLength = schema['maxLength'] as int?;
    if (maxLength != null && value.length > maxLength) {
      errors.add('String at $path must be at most $maxLength characters long');
      isValid = false;
    }

    // Check pattern
    final pattern = schema['pattern'] as String?;
    if (pattern != null) {
      final regex = RegExp(pattern);
      if (!regex.hasMatch(value)) {
        errors.add('String at $path does not match required pattern: $pattern');
        isValid = false;
      }
    }

    // Check format
    final format = schema['format'] as String?;
    if (format != null) {
      if (!_validateFormat(value, format, path, errors)) {
        isValid = false;
      }
    }

    return isValid;
  }

  /// Validates number constraints
  static bool _validateNumber(dynamic value, Map<String, dynamic> schema, String path, List<String> errors) {
    if (value is! num) return true;

    bool isValid = true;

    // Check minimum
    final minimum = schema['minimum'] as num?;
    if (minimum != null && value < minimum) {
      errors.add('Number at $path must be at least $minimum');
      isValid = false;
    }

    // Check maximum
    final maximum = schema['maximum'] as num?;
    if (maximum != null && value > maximum) {
      errors.add('Number at $path must be at most $maximum');
      isValid = false;
    }

    // Check exclusive minimum
    final exclusiveMinimum = schema['exclusiveMinimum'] as num?;
    if (exclusiveMinimum != null && value <= exclusiveMinimum) {
      errors.add('Number at $path must be greater than $exclusiveMinimum');
      isValid = false;
    }

    // Check exclusive maximum
    final exclusiveMaximum = schema['exclusiveMaximum'] as num?;
    if (exclusiveMaximum != null && value >= exclusiveMaximum) {
      errors.add('Number at $path must be less than $exclusiveMaximum');
      isValid = false;
    }

    return isValid;
  }

  /// Validates array constraints
  static bool _validateArray(dynamic value, Map<String, dynamic> schema, String path, List<String> errors) {
    if (value is! List) return true;

    bool isValid = true;

    // Check minimum items
    final minItems = schema['minItems'] as int?;
    if (minItems != null && value.length < minItems) {
      errors.add('Array at $path must have at least $minItems items');
      isValid = false;
    }

    // Check maximum items
    final maxItems = schema['maxItems'] as int?;
    if (maxItems != null && value.length > maxItems) {
      errors.add('Array at $path must have at most $maxItems items');
      isValid = false;
    }

    // Validate items
    final items = schema['items'] as Map<String, dynamic>?;
    if (items != null) {
      for (int i = 0; i < value.length; i++) {
        if (!_validateValue(value[i], items, '$path[$i]', errors)) {
          isValid = false;
        }
      }
    }

    return isValid;
  }

  /// Validates string format
  static bool _validateFormat(String value, String format, String path, List<String> errors) {
    switch (format) {
      case 'date-time':
        try {
          DateTime.parse(value);
        } catch (e) {
          errors.add('String at $path is not a valid date-time format');
          return false;
        }
        break;
      case 'email':
        final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
        if (!emailRegex.hasMatch(value)) {
          errors.add('String at $path is not a valid email format');
          return false;
        }
        break;
      case 'uri':
        try {
          Uri.parse(value);
        } catch (e) {
          errors.add('String at $path is not a valid URI format');
          return false;
        }
        break;
    }
    return true;
  }
}
