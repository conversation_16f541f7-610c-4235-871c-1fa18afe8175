import 'package:flutter/material.dart';
import 'dart:convert';
import '../widgets/calendar_widget.dart';
import 'runtime_calendar_widget.dart';

/// Runtime Property Discovery Serializer
/// Can serialize any Flutter widget dynamically without custom toJson/fromJson methods
class FlexibleWidgetSerializer {
  
  /// Custom serializers for specific widget types
  static final Map<Type, Function> _customSerializers = {
    
    // Container serialization
    Container: (Container w) => {
      'type': 'Container',
      'width': w.constraints?.maxWidth != double.infinity ? w.constraints?.maxWidth : null,
      'height': w.constraints?.maxHeight != double.infinity ? w.constraints?.maxHeight : null,
      'color': w.decoration != null && w.decoration is BoxDecoration 
          ? (w.decoration as BoxDecoration).color?.value 
          : null,
      'padding': w.padding != null ? _serializeEdgeInsets(w.padding!) : null,
      'margin': w.margin != null ? _serializeEdgeInsets(w.margin!) : null,
      'decoration': w.decoration != null ? _serializeBoxDecoration(w.decoration as BoxDecoration?) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Text serialization
    Text: (Text w) => {
      'type': 'Text',
      'data': w.data,
      'style': w.style != null ? {
        'fontSize': w.style!.fontSize,
        'color': w.style!.color?.value,
        'fontWeight': w.style!.fontWeight?.index,
        'fontFamily': w.style!.fontFamily,
      } : null,
      'textAlign': w.textAlign?.index,
      'maxLines': w.maxLines,
      'overflow': w.overflow?.index,
    },
    
    // Column serialization
    Column: (Column w) => {
      'type': 'Column',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
    // Row serialization
    Row: (Row w) => {
      'type': 'Row',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
    // CalendarWidget runtime discovery
    CalendarWidget: (CalendarWidget w) => {
      'type': 'CalendarWidget',
      'size': w.size.name,
      'initialSelectedDate': w.initialSelectedDate?.toIso8601String(),
      'borderRadius': w.borderRadius,
      'borderWidth': w.borderWidth,
      'hasBorder': w.hasBorder,
      'hasShadow': w.hasShadow,
      'backgroundColor': w.backgroundColor?.value,
      'headerColor': w.headerColor?.value,
      'selectedDateColor': w.selectedDateColor?.value,
      'todayColor': w.todayColor?.value,
      'headerTextColor': w.headerTextColor?.value,
      'weekdayLabelColor': w.weekdayLabelColor?.value,
      'dateTextColor': w.dateTextColor?.value,
      'width': w.width,
      'height': w.height,
      'showHeader': w.showHeader,
      'showNavigation': w.showNavigation,
      'showWeekdayLabels': w.showWeekdayLabels,
      'allowDateSelection': w.allowDateSelection,
      'allowMultipleSelection': w.allowMultipleSelection,
      'firstDayOfWeek': w.firstDayOfWeek,
      // Events would need special handling
      'events': w.events?.map((date, events) => MapEntry(
        date.toIso8601String().split('T')[0],
        events.map((e) => {
          'title': e.title,
          'description': e.description,
          'color': e.color?.value,
          'startTime': e.startTime?.toIso8601String(),
          'endTime': e.endTime?.toIso8601String(),
        }).toList(),
      )),
    },

    // RuntimeCalendarWidget runtime discovery
    RuntimeCalendarWidget: (RuntimeCalendarWidget w) => {
      'type': 'RuntimeCalendarWidget',
      'size': w.size.name,
      'initialSelectedDate': w.initialSelectedDate?.toIso8601String(),
      'borderRadius': w.borderRadius,
      'borderWidth': w.borderWidth,
      'hasBorder': w.hasBorder,
      'hasShadow': w.hasShadow,
      'backgroundColor': w.backgroundColor?.value,
      'headerColor': w.headerColor?.value,
      'selectedDateColor': w.selectedDateColor?.value,
      'todayColor': w.todayColor?.value,
      'headerTextColor': w.headerTextColor?.value,
      'weekdayLabelColor': w.weekdayLabelColor?.value,
      'dateTextColor': w.dateTextColor?.value,
      'width': w.width,
      'height': w.height,
      'showHeader': w.showHeader,
      'showNavigation': w.showNavigation,
      'showWeekdayLabels': w.showWeekdayLabels,
      'allowDateSelection': w.allowDateSelection,
      'allowMultipleSelection': w.allowMultipleSelection,
      'firstDayOfWeek': w.firstDayOfWeek,
      // Events would need special handling if implemented
      'events': w.events?.map((date, events) => MapEntry(
        date.toIso8601String().split('T')[0],
        events.map((e) => {
          'title': e.title,
          'description': e.description,
          'color': e.color?.value,
          'startTime': e.startTime?.toIso8601String(),
          'endTime': e.endTime?.toIso8601String(),
        }).toList(),
      )),
    },

    // IconButton serialization
    IconButton: (IconButton w) => {
      'type': 'IconButton',
      'iconSize': w.iconSize,
      'color': w.color?.value,
      'icon': w.icon.runtimeType.toString(), // Basic icon info
      'tooltip': w.tooltip,
    },
    
    // Expanded serialization
    Expanded: (Expanded w) => {
      'type': 'Expanded',
      'flex': w.flex,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // SizedBox serialization
    SizedBox: (SizedBox w) => {
      'type': 'SizedBox',
      'width': w.width,
      'height': w.height,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Padding serialization
    Padding: (Padding w) => {
      'type': 'Padding',
      'padding': _serializeEdgeInsets(w.padding),
      'child': w.child != null ? serialize(w.child!) : null,
    },
  };
  
  /// Main serialization method - discovers widget type at runtime
  static Map<String, dynamic> serialize(Widget widget) {
    final serializer = _customSerializers[widget.runtimeType];
    
    Map<String, dynamic> base = {
      'runtimeType': widget.runtimeType.toString(),
      'key': widget.key?.toString(),
      'hashCode': widget.hashCode,
    };
    
    if (serializer != null) {
      try {
        final serialized = serializer(widget);
        base.addAll(serialized);
      } catch (e) {
        base['serializationError'] = e.toString();
        base.addAll(_extractBasicProperties(widget));
      }
    } else {
      base['unsupported'] = true;
      base.addAll(_extractBasicProperties(widget));
    }
    
    return base;
  }
  
  /// Fallback property extraction for unsupported widgets
  static Map<String, dynamic> _extractBasicProperties(Widget widget) {
    return {
      'runtimeType': widget.runtimeType.toString(),
      'hashCode': widget.hashCode,
      'toString': widget.toString(),
    };
  }
  
  /// Helper: Serialize EdgeInsets
  static Map<String, dynamic> _serializeEdgeInsets(EdgeInsetsGeometry insets) {
    final resolved = insets.resolve(TextDirection.ltr);
    return {
      'left': resolved.left,
    'top': resolved.top,
    'right': resolved.right,
    'bottom': resolved.bottom,
    };
  }
  
  /// Helper: Serialize BoxDecoration
  static Map<String, dynamic>? _serializeBoxDecoration(BoxDecoration? decoration) {
    if (decoration == null) return null;
    
    return {
      'color': decoration.color?.value,
      'borderRadius': decoration.borderRadius?.toString(),
      'border': decoration.border?.toString(),
      'boxShadow': decoration.boxShadow?.map((shadow) => {
        'color': shadow.color.value,
        'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
        'blurRadius': shadow.blurRadius,
        'spreadRadius': shadow.spreadRadius,
      }).toList(),
    };
  }
  
  /// Convert to pretty JSON string
  static String toJsonString(Widget widget, {bool prettyPrint = false}) {
    final json = serialize(widget);
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }
  
  /// Deserialize JSON back to Widget (limited support)
  static Widget? deserialize(Map<String, dynamic> json) {
    final type = json['type'];

    switch (type) {
      case 'RuntimeCalendarWidget':
        return RuntimeCalendarWidget.fromRuntimeJson(json);

      case 'Container':
        return Container(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          color: json['color'] != null ? Color(json['color']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Text':
        return Text(
          json['data'] ?? '',
          style: json['style'] != null ? TextStyle(
            fontSize: json['style']['fontSize']?.toDouble(),
            color: json['style']['color'] != null ? Color(json['style']['color']) : null,
          ) : null,
        );

      case 'SizedBox':
        return SizedBox(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      default:
        // Unsupported widget type
        return Container(
          child: Text('Unsupported widget: $type'),
        );
    }
  }

  /// Deserialize from JSON string
  static Widget? deserializeFromString(String jsonString) {
    try {
      final json = jsonDecode(jsonString);
      return deserialize(json);
    } catch (e) {
      return Container(
        child: Text('Deserialization error: $e'),
      );
    }
  }

  /// Get serialization statistics
  static Map<String, dynamic> getSerializationStats(Widget widget) {
    final json = serialize(widget);
    
    int supportedWidgets = 0;
    int unsupportedWidgets = 0;
    Set<String> widgetTypes = {};
    
    void countWidgets(Map<String, dynamic> data) {
      if (data.containsKey('type')) {
        widgetTypes.add(data['type']);
        if (data.containsKey('unsupported') && data['unsupported'] == true) {
          unsupportedWidgets++;
        } else {
          supportedWidgets++;
        }
      }
      
      // Recursively count children
      if (data.containsKey('children') && data['children'] is List) {
        for (var child in data['children']) {
          if (child is Map<String, dynamic>) {
            countWidgets(child);
          }
        }
      }
      
      if (data.containsKey('child') && data['child'] is Map<String, dynamic>) {
        countWidgets(data['child']);
      }
    }
    
    countWidgets(json);
    
    return {
      'totalWidgets': supportedWidgets + unsupportedWidgets,
      'supportedWidgets': supportedWidgets,
      'unsupportedWidgets': unsupportedWidgets,
      'supportRate': supportedWidgets / (supportedWidgets + unsupportedWidgets) * 100,
      'widgetTypes': widgetTypes.toList(),
      'jsonSize': jsonEncode(json).length,
    };
  }
}
