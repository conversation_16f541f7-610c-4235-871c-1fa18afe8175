/// Model representing a widget configuration
class WidgetConfig {
  /// The type of widget
  final String widgetType;
  
  /// The schema version
  final String schemaVersion;
  
  /// The configuration properties
  final Map<String, dynamic> properties;
  
  /// Metadata about the configuration
  final Map<String, dynamic>? metadata;
  
  /// When this configuration was created
  final DateTime createdAt;
  
  /// When this configuration was last modified
  final DateTime? modifiedAt;

  const WidgetConfig({
    required this.widgetType,
    required this.schemaVersion,
    required this.properties,
    this.metadata,
    required this.createdAt,
    this.modifiedAt,
  });

  /// Creates a WidgetConfig from JSON
  factory WidgetConfig.fromJson(Map<String, dynamic> json) {
    return WidgetConfig(
      widgetType: json['widgetType'] as String,
      schemaVersion: json['schemaVersion'] as String,
      properties: Map<String, dynamic>.from(json['properties'] as Map),
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata'] as Map)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      modifiedAt: json['modifiedAt'] != null 
          ? DateTime.parse(json['modifiedAt'] as String)
          : null,
    );
  }

  /// Converts the WidgetConfig to JSON
  Map<String, dynamic> toJson() {
    return {
      'widgetType': widgetType,
      'schemaVersion': schemaVersion,
      'properties': properties,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt?.toIso8601String(),
    };
  }

  /// Creates a copy of this config with modified properties
  WidgetConfig copyWith({
    String? widgetType,
    String? schemaVersion,
    Map<String, dynamic>? properties,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? modifiedAt,
  }) {
    return WidgetConfig(
      widgetType: widgetType ?? this.widgetType,
      schemaVersion: schemaVersion ?? this.schemaVersion,
      properties: properties ?? Map<String, dynamic>.from(this.properties),
      metadata: metadata ?? (this.metadata != null ? Map<String, dynamic>.from(this.metadata!) : null),
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
    );
  }

  /// Merges this config with another config
  WidgetConfig merge(WidgetConfig other) {
    final mergedProperties = Map<String, dynamic>.from(properties);
    mergedProperties.addAll(other.properties);

    final mergedMetadata = metadata != null 
        ? Map<String, dynamic>.from(metadata!)
        : <String, dynamic>{};
    if (other.metadata != null) {
      mergedMetadata.addAll(other.metadata!);
    }

    return WidgetConfig(
      widgetType: other.widgetType,
      schemaVersion: other.schemaVersion,
      properties: mergedProperties,
      metadata: mergedMetadata.isNotEmpty ? mergedMetadata : null,
      createdAt: createdAt,
      modifiedAt: DateTime.now(),
    );
  }

  /// Gets a property value with type safety
  T? getProperty<T>(String key, {T? defaultValue}) {
    final value = properties[key];
    if (value is T) return value;
    return defaultValue;
  }

  /// Sets a property value
  WidgetConfig setProperty(String key, dynamic value) {
    final newProperties = Map<String, dynamic>.from(properties);
    newProperties[key] = value;
    
    return copyWith(
      properties: newProperties,
      modifiedAt: DateTime.now(),
    );
  }

  /// Removes a property
  WidgetConfig removeProperty(String key) {
    final newProperties = Map<String, dynamic>.from(properties);
    newProperties.remove(key);
    
    return copyWith(
      properties: newProperties,
      modifiedAt: DateTime.now(),
    );
  }

  /// Gets metadata value with type safety
  T? getMetadata<T>(String key, {T? defaultValue}) {
    if (metadata == null) return defaultValue;
    final value = metadata![key];
    if (value is T) return value;
    return defaultValue;
  }

  /// Sets a metadata value
  WidgetConfig setMetadata(String key, dynamic value) {
    final newMetadata = metadata != null 
        ? Map<String, dynamic>.from(metadata!)
        : <String, dynamic>{};
    newMetadata[key] = value;
    
    return copyWith(
      metadata: newMetadata,
      modifiedAt: DateTime.now(),
    );
  }

  /// Validates the configuration
  bool validate({List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    if (widgetType.isEmpty) {
      errors?.add('widgetType cannot be empty');
      isValid = false;
    }

    if (schemaVersion.isEmpty) {
      errors?.add('schemaVersion cannot be empty');
      isValid = false;
    }

    return isValid;
  }

  /// Gets a summary of the configuration
  Map<String, dynamic> getSummary() {
    return {
      'widgetType': widgetType,
      'schemaVersion': schemaVersion,
      'propertyCount': properties.length,
      'hasMetadata': metadata != null,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt?.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is WidgetConfig &&
        other.widgetType == widgetType &&
        other.schemaVersion == schemaVersion &&
        _mapEquals(other.properties, properties) &&
        _mapEquals(other.metadata, metadata) &&
        other.createdAt == createdAt &&
        other.modifiedAt == modifiedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      widgetType,
      schemaVersion,
      properties,
      metadata,
      createdAt,
      modifiedAt,
    );
  }

  @override
  String toString() {
    return 'WidgetConfig(widgetType: $widgetType, schemaVersion: $schemaVersion, properties: ${properties.length} items)';
  }

  /// Helper method to compare maps
  bool _mapEquals(Map<String, dynamic>? a, Map<String, dynamic>? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) {
        return false;
      }
    }
    
    return true;
  }
}
