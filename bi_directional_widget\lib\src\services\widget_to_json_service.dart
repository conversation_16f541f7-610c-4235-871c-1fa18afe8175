import 'package:flutter/material.dart';
import 'dart:convert';
import '../core/json_serializable_widget.dart';
import '../exceptions/widget_conversion_exception.dart';
import 'widget_registry_service.dart';

/// Service for converting widgets to JSON
/// 
/// This service provides methods to convert widget instances to JSON representations
/// using registered converters or the widget's own toJson method.
class WidgetToJsonService {
  static final WidgetRegistryService _registry = WidgetRegistryService();

  /// Converts a widget to JSON using registered converters
  /// 
  /// [widget] The widget to convert
  /// Returns the JSON representation as a Map
  /// Throws [WidgetConversionException] if conversion fails
  static Map<String, dynamic> convert(Widget widget) {
    try {
      // First, try to use the widget's own toJson method if it implements JsonSerializableWidget
      if (widget is JsonSerializableWidget) {
        final jsonWidget = widget as JsonSerializableWidget;
        final json = jsonWidget.toJson();
        // Add metadata
        json['widgetType'] = jsonWidget.widgetType;
        json['schemaVersion'] = jsonWidget.schemaVersion;
        json['generatedAt'] = DateTime.now().toIso8601String();
        return json;
      }

      // Otherwise, try to find a registered converter
      final converter = _registry.getConverterForWidget(widget);
      if (converter == null) {
        throw WidgetConversionException(
          'No converter registered for widget type: ${widget.runtimeType}',
          widgetType: widget.runtimeType.toString(),
        );
      }

      final json = converter.widgetToJson(widget);
      // Add metadata
      json['widgetType'] = converter.widgetType;
      json['generatedAt'] = DateTime.now().toIso8601String();
      return json;
    } catch (e, stackTrace) {
      throw WidgetConversionException(
        'Failed to convert widget to JSON: $e',
        widgetType: widget.runtimeType.toString(),
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Converts a widget to a JSON string
  /// 
  /// [widget] The widget to convert
  /// [prettyPrint] Whether to format the JSON with indentation
  /// Returns the JSON representation as a String
  /// Throws [WidgetConversionException] if conversion fails
  static String convertToString(Widget widget, {bool prettyPrint = false}) {
    final json = convert(widget);
    
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    
    return jsonEncode(json);
  }

  /// Converts multiple widgets to JSON
  /// 
  /// [widgets] List of widgets to convert
  /// Returns a list of JSON representations
  /// Throws [WidgetConversionException] if any conversion fails
  static List<Map<String, dynamic>> convertMultiple(List<Widget> widgets) {
    final results = <Map<String, dynamic>>[];
    
    for (int i = 0; i < widgets.length; i++) {
      try {
        results.add(convert(widgets[i]));
      } catch (e) {
        throw WidgetConversionException(
          'Failed to convert widget at index $i: $e',
          originalException: e,
        );
      }
    }
    
    return results;
  }

  /// Converts a widget tree to JSON recursively
  /// 
  /// [widget] The root widget to convert
  /// [maxDepth] Maximum depth to traverse (prevents infinite recursion)
  /// Returns the JSON representation with child widgets included
  static Map<String, dynamic> convertTree(Widget widget, {int maxDepth = 10}) {
    if (maxDepth <= 0) {
      return {'error': 'Maximum depth reached'};
    }

    final json = convert(widget);
    
    // Try to extract child widgets if the widget has them
    final children = _extractChildren(widget);
    if (children.isNotEmpty) {
      json['children'] = children.map((child) => 
        convertTree(child, maxDepth: maxDepth - 1)
      ).toList();
    }
    
    return json;
  }

  /// Extracts child widgets from a widget (basic implementation)
  /// 
  /// This is a simplified implementation that handles common widget types.
  /// For a complete implementation, you would need to handle all widget types.
  static List<Widget> _extractChildren(Widget widget) {
    final children = <Widget>[];
    
    // Handle common widget types that have children
    if (widget is SingleChildRenderObjectWidget) {
      if (widget.child != null) {
        children.add(widget.child!);
      }
    } else if (widget is MultiChildRenderObjectWidget) {
      children.addAll(widget.children);
    } else if (widget is StatefulWidget) {
      // For stateful widgets, we can't easily extract children without building
      // This would require a more sophisticated approach
    } else if (widget is StatelessWidget) {
      // For stateless widgets, we can't easily extract children without building
      // This would require a more sophisticated approach
    }
    
    return children;
  }

  /// Validates that a widget can be converted to JSON
  /// 
  /// [widget] The widget to validate
  /// [errors] Optional list to collect validation errors
  /// Returns true if the widget can be converted, false otherwise
  static bool canConvert(Widget widget, {List<String>? errors}) {
    errors?.clear();
    
    // Check if widget implements JsonSerializableWidget
    if (widget is JsonSerializableWidget) {
      final jsonWidget = widget as JsonSerializableWidget;
      return jsonWidget.validate(errors: errors);
    }
    
    // Check if there's a registered converter
    final converter = _registry.getConverterForWidget(widget);
    if (converter == null) {
      errors?.add('No converter registered for widget type: ${widget.runtimeType}');
      return false;
    }
    
    return true;
  }

  /// Gets conversion statistics
  /// 
  /// Returns information about available converters and conversion capabilities
  static Map<String, dynamic> getStats() {
    final registryStats = _registry.getStats();
    
    return {
      'availableConverters': registryStats['totalConverters'],
      'supportedTypes': registryStats['registeredTypes'],
      'registryStats': registryStats,
    };
  }

  /// Creates a minimal JSON representation with only changed properties
  /// 
  /// [widget] The widget to convert
  /// Returns a minimal JSON with only non-default properties
  static Map<String, dynamic> convertMinimal(Widget widget) {
    if (widget is JsonSerializableWidget) {
      final jsonWidget = widget as JsonSerializableWidget;
      final delta = jsonWidget.getDelta();
      delta['widgetType'] = jsonWidget.widgetType;
      delta['schemaVersion'] = jsonWidget.schemaVersion;
      return delta;
    }
    
    // For non-JsonSerializableWidget, return full conversion
    return convert(widget);
  }
}
