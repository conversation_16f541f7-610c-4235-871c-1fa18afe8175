import 'package:flutter/material.dart';
import '../core/widget_json_converter.dart';
import '../exceptions/widget_conversion_exception.dart';
import '../exceptions/json_validation_exception.dart';
import 'calendar_widget.dart';

/// Converter for CalendarWidget that handles bidirectional JSON conversion
class CalendarWidgetConverter extends WidgetJsonConverter<CalendarWidget> {
  @override
  String get widgetType => 'calendar';

  @override
  Map<String, dynamic> get jsonSchema => {
    'type': 'object',
    'properties': {
      'widgetType': {'type': 'string', 'const': 'calendar'},
      'schemaVersion': {'type': 'string'},
      'size': {
        'type': 'string',
        'enum': ['small', 'medium', 'large']
      },
      'initialSelectedDate': {'type': 'string', 'format': 'date-time'},
      'initialDisplayDate': {'type': 'string', 'format': 'date-time'},
      'allowDateSelection': {'type': 'boolean'},
      'allowMultipleSelection': {'type': 'boolean'},
      'initialSelectedDates': {
        'type': 'array',
        'items': {'type': 'string', 'format': 'date-time'}
      },
      'minDate': {'type': 'string', 'format': 'date-time'},
      'maxDate': {'type': 'string', 'format': 'date-time'},
      'events': {
        'type': 'object',
        'additionalProperties': {
          'type': 'array',
          'items': {
            'type': 'object',
            'properties': {
              'title': {'type': 'string'},
              'description': {'type': 'string'},
              'color': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
              'startTime': {'type': 'string', 'format': 'date-time'},
              'endTime': {'type': 'string', 'format': 'date-time'}
            },
            'required': ['title']
          }
        }
      },
      'showHeader': {'type': 'boolean'},
      'showNavigation': {'type': 'boolean'},
      'showWeekdayLabels': {'type': 'boolean'},
      'firstDayOfWeek': {'type': 'integer', 'minimum': 0, 'maximum': 6},
      'locale': {'type': 'string'},
      'headingFontSize': {'type': 'number'},
      'bodyFontSize': {'type': 'number'},
      'labelFontSize': {'type': 'number'},
      'headingFontWeight': {'type': 'integer'},
      'bodyFontWeight': {'type': 'integer'},
      'labelFontWeight': {'type': 'integer'},
      'borderWidth': {'type': 'number'},
      'borderColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'borderRadius': {'type': 'number'},
      'hasBorder': {'type': 'boolean'},
      'boxShadow': {
        'type': 'array',
        'items': {
          'type': 'object',
          'properties': {
            'color': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
            'offset': {
              'type': 'object',
              'properties': {
                'dx': {'type': 'number'},
                'dy': {'type': 'number'}
              }
            },
            'blurRadius': {'type': 'number'},
            'spreadRadius': {'type': 'number'}
          }
        }
      },
      'hasShadow': {'type': 'boolean'},
      'backgroundColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'headerColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'headerTextColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'selectedDateColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'selectedDateTextColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'todayColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'todayTextColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'weekdayLabelColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'dateColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'dateTextColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'disabledDateColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'disabledDateTextColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'eventIndicatorColor': {'oneOf': [{'type': 'string'}, {'type': 'object'}]},
      'width': {'type': 'number'},
      'height': {'type': 'number'},
      'padding': {
        'type': 'object',
        'properties': {
          'left': {'type': 'number'},
          'top': {'type': 'number'},
          'right': {'type': 'number'},
          'bottom': {'type': 'number'}
        }
      },
      'margin': {
        'type': 'object',
        'properties': {
          'left': {'type': 'number'},
          'top': {'type': 'number'},
          'right': {'type': 'number'},
          'bottom': {'type': 'number'}
        }
      }
    },
    'required': ['widgetType']
  };

  @override
  Map<String, dynamic> widgetToJson(CalendarWidget widget) {
    try {
      return widget.toJson();
    } catch (e, stackTrace) {
      throw WidgetConversionException(
        'Failed to convert CalendarWidget to JSON: $e',
        widgetType: widgetType,
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  CalendarWidget jsonToWidget(Map<String, dynamic> json) {
    try {
      return CalendarWidget.fromJson(json);
    } catch (e, stackTrace) {
      throw WidgetConversionException(
        'Failed to convert JSON to CalendarWidget: $e',
        widgetType: widgetType,
        originalException: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  bool validateJson(Map<String, dynamic> json, {List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    // Check required fields
    if (!json.containsKey('widgetType')) {
      errors?.add('Missing required field: widgetType');
      isValid = false;
    } else if (json['widgetType'] != 'calendar') {
      errors?.add('Invalid widgetType: expected "calendar", got "${json['widgetType']}"');
      isValid = false;
    }

    // Validate size enum
    if (json.containsKey('size')) {
      final size = json['size'];
      if (size is! String || !['small', 'medium', 'large'].contains(size)) {
        errors?.add('Invalid size: must be one of "small", "medium", "large"');
        isValid = false;
      }
    }

    // Validate firstDayOfWeek range
    if (json.containsKey('firstDayOfWeek')) {
      final firstDay = json['firstDayOfWeek'];
      if (firstDay is! int || firstDay < 0 || firstDay > 6) {
        errors?.add('Invalid firstDayOfWeek: must be an integer between 0 and 6');
        isValid = false;
      }
    }

    // Validate date strings
    final dateFields = [
      'initialSelectedDate',
      'initialDisplayDate',
      'minDate',
      'maxDate'
    ];

    for (final field in dateFields) {
      if (json.containsKey(field) && json[field] != null) {
        try {
          DateTime.parse(json[field] as String);
        } catch (e) {
          errors?.add('Invalid date format for $field: ${json[field]}');
          isValid = false;
        }
      }
    }

    // Validate initialSelectedDates array
    if (json.containsKey('initialSelectedDates') && json['initialSelectedDates'] != null) {
      final dates = json['initialSelectedDates'];
      if (dates is! List) {
        errors?.add('initialSelectedDates must be an array');
        isValid = false;
      } else {
        for (int i = 0; i < dates.length; i++) {
          try {
            DateTime.parse(dates[i] as String);
          } catch (e) {
            errors?.add('Invalid date format in initialSelectedDates[$i]: ${dates[i]}');
            isValid = false;
          }
        }
      }
    }

    // Validate date range consistency
    if (json.containsKey('minDate') && json.containsKey('maxDate') &&
        json['minDate'] != null && json['maxDate'] != null) {
      try {
        final minDate = DateTime.parse(json['minDate'] as String);
        final maxDate = DateTime.parse(json['maxDate'] as String);
        if (minDate.isAfter(maxDate)) {
          errors?.add('minDate cannot be after maxDate');
          isValid = false;
        }
      } catch (e) {
        // Date parsing errors already handled above
      }
    }

    // Validate numeric fields
    final numericFields = [
      'headingFontSize',
      'bodyFontSize',
      'labelFontSize',
      'borderWidth',
      'borderRadius',
      'width',
      'height'
    ];

    for (final field in numericFields) {
      if (json.containsKey(field) && json[field] != null) {
        final value = json[field];
        if (value is! num || value < 0) {
          errors?.add('$field must be a non-negative number');
          isValid = false;
        }
      }
    }

    return isValid;
  }
}
