/// Model representing a JSON schema for widget configuration
class JsonWidgetSchema {
  /// The schema identifier
  final String id;
  
  /// The schema version
  final String version;
  
  /// The widget type this schema applies to
  final String widgetType;
  
  /// The JSON schema definition
  final Map<String, dynamic> schema;
  
  /// Description of the schema
  final String? description;
  
  /// Examples of valid configurations
  final List<Map<String, dynamic>>? examples;
  
  /// When this schema was created
  final DateTime createdAt;

  const JsonWidgetSchema({
    required this.id,
    required this.version,
    required this.widgetType,
    required this.schema,
    this.description,
    this.examples,
    required this.createdAt,
  });

  /// Creates a JsonWidgetSchema from JSON
  factory JsonWidgetSchema.fromJson(Map<String, dynamic> json) {
    return JsonWidgetSchema(
      id: json['id'] as String,
      version: json['version'] as String,
      widgetType: json['widgetType'] as String,
      schema: Map<String, dynamic>.from(json['schema'] as Map),
      description: json['description'] as String?,
      examples: json['examples'] != null
          ? (json['examples'] as List)
              .map((e) => Map<String, dynamic>.from(e as Map))
              .toList()
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// Converts the JsonWidgetSchema to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'version': version,
      'widgetType': widgetType,
      'schema': schema,
      'description': description,
      'examples': examples,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Gets the required properties from the schema
  List<String> getRequiredProperties() {
    final properties = schema['properties'] as Map<String, dynamic>?;
    final required = schema['required'] as List<dynamic>?;
    
    if (required != null) {
      return required.cast<String>();
    }
    
    return [];
  }

  /// Gets the optional properties from the schema
  List<String> getOptionalProperties() {
    final properties = schema['properties'] as Map<String, dynamic>?;
    final required = getRequiredProperties();
    
    if (properties != null) {
      return properties.keys.where((key) => !required.contains(key)).toList();
    }
    
    return [];
  }

  /// Gets all property names from the schema
  List<String> getAllProperties() {
    final properties = schema['properties'] as Map<String, dynamic>?;
    return properties?.keys.toList() ?? [];
  }

  /// Gets the schema for a specific property
  Map<String, dynamic>? getPropertySchema(String propertyName) {
    final properties = schema['properties'] as Map<String, dynamic>?;
    return properties?[propertyName] as Map<String, dynamic>?;
  }

  /// Gets the type of a property
  String? getPropertyType(String propertyName) {
    final propertySchema = getPropertySchema(propertyName);
    return propertySchema?['type'] as String?;
  }

  /// Checks if a property is required
  bool isPropertyRequired(String propertyName) {
    return getRequiredProperties().contains(propertyName);
  }

  /// Gets the default value for a property
  dynamic getPropertyDefault(String propertyName) {
    final propertySchema = getPropertySchema(propertyName);
    return propertySchema?['default'];
  }

  /// Gets the description for a property
  String? getPropertyDescription(String propertyName) {
    final propertySchema = getPropertySchema(propertyName);
    return propertySchema?['description'] as String?;
  }

  /// Gets enum values for a property
  List<dynamic>? getPropertyEnum(String propertyName) {
    final propertySchema = getPropertySchema(propertyName);
    return propertySchema?['enum'] as List<dynamic>?;
  }

  /// Validates that this schema is well-formed
  bool validate({List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    if (id.isEmpty) {
      errors?.add('Schema id cannot be empty');
      isValid = false;
    }

    if (version.isEmpty) {
      errors?.add('Schema version cannot be empty');
      isValid = false;
    }

    if (widgetType.isEmpty) {
      errors?.add('Widget type cannot be empty');
      isValid = false;
    }

    if (schema.isEmpty) {
      errors?.add('Schema definition cannot be empty');
      isValid = false;
    }

    // Validate schema structure
    if (!schema.containsKey('type')) {
      errors?.add('Schema must have a type field');
      isValid = false;
    }

    if (schema['type'] != 'object') {
      errors?.add('Root schema type must be "object"');
      isValid = false;
    }

    if (!schema.containsKey('properties')) {
      errors?.add('Schema must have a properties field');
      isValid = false;
    }

    return isValid;
  }

  /// Creates a template configuration based on this schema
  Map<String, dynamic> createTemplate() {
    final template = <String, dynamic>{
      'widgetType': widgetType,
      'schemaVersion': version,
    };

    final properties = schema['properties'] as Map<String, dynamic>?;
    if (properties != null) {
      properties.forEach((key, propertySchema) {
        final propSchema = propertySchema as Map<String, dynamic>;
        final defaultValue = propSchema['default'];
        
        if (defaultValue != null) {
          template[key] = defaultValue;
        } else {
          // Generate default based on type
          final type = propSchema['type'] as String?;
          template[key] = _getDefaultForType(type);
        }
      });
    }

    return template;
  }

  /// Gets a default value for a given type
  dynamic _getDefaultForType(String? type) {
    switch (type) {
      case 'string':
        return '';
      case 'number':
        return 0.0;
      case 'integer':
        return 0;
      case 'boolean':
        return false;
      case 'array':
        return [];
      case 'object':
        return {};
      default:
        return null;
    }
  }

  /// Gets schema statistics
  Map<String, dynamic> getStats() {
    final properties = schema['properties'] as Map<String, dynamic>?;
    final required = getRequiredProperties();
    
    return {
      'totalProperties': properties?.length ?? 0,
      'requiredProperties': required.length,
      'optionalProperties': (properties?.length ?? 0) - required.length,
      'hasExamples': examples != null && examples!.isNotEmpty,
      'exampleCount': examples?.length ?? 0,
    };
  }

  /// Creates a copy of this schema with modifications
  JsonWidgetSchema copyWith({
    String? id,
    String? version,
    String? widgetType,
    Map<String, dynamic>? schema,
    String? description,
    List<Map<String, dynamic>>? examples,
    DateTime? createdAt,
  }) {
    return JsonWidgetSchema(
      id: id ?? this.id,
      version: version ?? this.version,
      widgetType: widgetType ?? this.widgetType,
      schema: schema ?? Map<String, dynamic>.from(this.schema),
      description: description ?? this.description,
      examples: examples ?? (this.examples != null ? List<Map<String, dynamic>>.from(this.examples!) : null),
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is JsonWidgetSchema &&
        other.id == id &&
        other.version == version &&
        other.widgetType == widgetType &&
        _mapEquals(other.schema, schema) &&
        other.description == description &&
        _listEquals(other.examples, examples) &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      version,
      widgetType,
      schema,
      description,
      examples,
      createdAt,
    );
  }

  @override
  String toString() {
    return 'JsonWidgetSchema(id: $id, widgetType: $widgetType, version: $version)';
  }

  /// Helper method to compare maps
  bool _mapEquals(Map<String, dynamic> a, Map<String, dynamic> b) {
    if (a.length != b.length) return false;
    
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) {
        return false;
      }
    }
    
    return true;
  }

  /// Helper method to compare lists
  bool _listEquals(List<Map<String, dynamic>>? a, List<Map<String, dynamic>>? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    
    for (int i = 0; i < a.length; i++) {
      if (!_mapEquals(a[i], b[i])) return false;
    }
    
    return true;
  }
}
