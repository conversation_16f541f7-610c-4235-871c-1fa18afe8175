import 'package:flutter/foundation.dart';
import '../models/object_creation_model.dart';
import '../services/object_creation_service.dart';
import '../utils/logger.dart';
import 'base_provider.dart';

class ObjectCreationProvider extends BaseProvider {
  final ObjectCreationService _objectCreationService = ObjectCreationService();

  // Data storage
  List<ObjectCreationModel> _objects = [];
  ObjectCreationModel? _currentObject;
  String? _currentJobId;
  String? _currentUserIntent;

  // Specific loading states for different operations
  bool _isLoadingExtraction = false;
  bool _isLoadingJobEntities = false;
  bool _isUpdatingEntity = false;
  bool _isDeletingEntity = false;

  // Getters
  List<ObjectCreationModel> get objects => _objects;
  ObjectCreationModel? get currentObject => _currentObject;
  String? get currentJobId => _currentJobId;
  String? get currentUserIntent => _currentUserIntent;
  int get objectCount => _objects.length;
  bool get hasObjects => _objects.isNotEmpty;

  // Specific loading state getters
  bool get isLoadingExtraction => _isLoadingExtraction;
  bool get isLoadingJobEntities => _isLoadingJobEntities;
  bool get isUpdatingEntity => _isUpdatingEntity;
  bool get isDeletingEntity => _isDeletingEntity;

  // Methods to set specific loading states
  void setLoadingExtraction(bool value) {
    if (_isLoadingExtraction != value) {
      _isLoadingExtraction = value;
      notifyListeners();
    }
  }

  void setLoadingJobEntities(bool value) {
    if (_isLoadingJobEntities != value) {
      _isLoadingJobEntities = value;
      notifyListeners();
    }
  }

  void setUpdatingEntity(bool value) {
    if (_isUpdatingEntity != value) {
      _isUpdatingEntity = value;
      notifyListeners();
    }
  }

  void setDeletingEntity(bool value) {
    if (_isDeletingEntity != value) {
      _isDeletingEntity = value;
      notifyListeners();
    }
  }

  // Constructor
  ObjectCreationProvider() {
    Logger.info('ObjectCreationProvider initialized');
  }

  /// Execute complete extraction workflow with user intent
  Future<bool> executeCompleteExtractionWorkflow({
    required String userIntent,
  }) async {
    setLoadingExtraction(true);
    clearError();

    final result = await runWithLoadingAndErrorHandling<ObjectCreationResponse>(
      () => _objectCreationService.executeCompleteExtractionWorkflow(
        userIntent: userIntent,
      ),
      context: 'ObjectCreationProvider.executeCompleteExtractionWorkflow',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success && result.data != null) {
      _objects = result.data!;
      _currentUserIntent = userIntent;
      Logger.info('Successfully extracted ${_objects.length} objects');
      setLoadingExtraction(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to extract entities';
      setError(errorMessage);
      Logger.error('Entity extraction failed: $errorMessage');
    } else {
      setError('Failed to extract entities. Please try again.');
      Logger.error('Entity extraction failed: No result returned');
    }

    setLoadingExtraction(false);
    return false;
  }

  /// Get entities for a specific job
  Future<bool> getJobEntities(String jobId) async {
    setLoadingJobEntities(true);
    clearError();

    final result = await runWithLoadingAndErrorHandling<ObjectCreationResponse>(
      () => _objectCreationService.getJobEntities(jobId),
      context: 'ObjectCreationProvider.getJobEntities',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success && result.data != null) {
      _objects = result.data!;
      _currentJobId = jobId;
      Logger.info('Successfully fetched ${_objects.length} entities for job: $jobId');
      setLoadingJobEntities(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to fetch job entities';
      setError(errorMessage);
      Logger.error('Failed to fetch job entities: $errorMessage');
    } else {
      setError('Failed to fetch job entities. Please try again.');
      Logger.error('Failed to fetch job entities: No result returned');
    }

    setLoadingJobEntities(false);
    return false;
  }

  /// Update an existing entity
  Future<bool> updateEntity({
    required String jobId,
    required String entityId,
    required Map<String, dynamic> updateData,
  }) async {
    setUpdatingEntity(true);
    clearError();

    final result = await runWithLoadingAndErrorHandling<ObjectCreationResponse>(
      () => _objectCreationService.updateEntity(
        jobId: jobId,
        entityId: entityId,
        updateData: updateData,
      ),
      context: 'ObjectCreationProvider.updateEntity',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success) {
      // Update the local object if we have the updated data
      if (result.singleData != null) {
        final index = _objects.indexWhere((obj) => obj.id == entityId);
        if (index != -1) {
          _objects[index] = result.singleData!;
        }
      }
      Logger.info('Successfully updated entity: $entityId');
      setUpdatingEntity(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to update entity';
      setError(errorMessage);
      Logger.error('Failed to update entity: $errorMessage');
    } else {
      setError('Failed to update entity. Please try again.');
      Logger.error('Failed to update entity: No result returned');
    }

    setUpdatingEntity(false);
    return false;
  }

  /// Delete an entity
  Future<bool> deleteEntity({
    required String jobId,
    required String entityId,
  }) async {
    setDeletingEntity(true);
    clearError();

    final result = await runWithLoadingAndErrorHandling<ObjectCreationResponse>(
      () => _objectCreationService.deleteEntity(
        jobId: jobId,
        entityId: entityId,
      ),
      context: 'ObjectCreationProvider.deleteEntity',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success) {
      // Remove the object from local list
      _objects.removeWhere((obj) => obj.id == entityId);
      Logger.info('Successfully deleted entity: $entityId');
      setDeletingEntity(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to delete entity';
      setError(errorMessage);
      Logger.error('Failed to delete entity: $errorMessage');
    } else {
      setError('Failed to delete entity. Please try again.');
      Logger.error('Failed to delete entity: No result returned');
    }

    setDeletingEntity(false);
    return false;
  }

  /// Set current object for detailed view
  void setCurrentObject(ObjectCreationModel? object) {
    _currentObject = object;
    notifyListeners();
  }

  /// Clear all data
  void clearData() {
    _objects.clear();
    _currentObject = null;
    _currentJobId = null;
    _currentUserIntent = null;
    clearError();
    notifyListeners();
    Logger.info('ObjectCreationProvider data cleared');
  }

  /// Refresh data based on current context
  Future<bool> refreshData() async {
    if (_currentJobId != null) {
      return await getJobEntities(_currentJobId!);
    } else if (_currentUserIntent != null) {
      return await executeCompleteExtractionWorkflow(
        userIntent: _currentUserIntent!,
      );
    }
    return false;
  }
}
