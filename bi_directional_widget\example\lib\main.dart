import 'package:flutter/material.dart';
import 'package:bi_directional_widget/bi_directional_widget.dart';
import 'runtime_comparison_demo.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Bi-Directional Widget Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const CalendarDemo(),
    );
  }
}

class CalendarDemo extends StatefulWidget {
  const CalendarDemo({super.key});

  @override
  State<CalendarDemo> createState() => _CalendarDemoState();
}

class _CalendarDemoState extends State<CalendarDemo> {
  CalendarWidget? _currentCalendar;
  String _jsonOutput = '';
  CalendarWidget? _recreatedCalendar;

  @override
  void initState() {
    super.initState();
    _createInitialCalendar();
  }

  void _createInitialCalendar() {
    // Create a simple calendar with minimal configuration to test JSON generation
    _currentCalendar = CalendarWidget(
      size: CalendarSize.medium,
      initialSelectedDate: DateTime.now(),
      borderRadius: 16.0,
      borderWidth: 1.0,
      hasBorder: true,
      hasShadow: true,
      selectedDateColor: Colors.blue,
      todayColor: Colors.orange.shade100,
      headerColor: Colors.white,
      onDateSelected: (date) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Selected: ${date.toString().split(' ')[0]}')),
        );
      },
    );

    _generateJson();
  }

  void _generateJson() {
    if (_currentCalendar != null) {
      try {
        // print('Generating JSON for calendar widget...');
        final json = _currentCalendar!.toJson();
        // print('JSON generated successfully: ${json.keys}');

        setState(() {
          _jsonOutput = _currentCalendar!.toJsonString(prettyPrint: true);
        });

        // print('JSON string length: ${_jsonOutput.length}');

        // Recreate widget from JSON to demonstrate bidirectional conversion
        _recreatedCalendar = CalendarWidget.fromJson(json);
        // print('Widget recreated successfully');
      } catch (e, stackTrace) {
        // print('Error generating JSON: $e');
        // print('Stack trace: $stackTrace');
        setState(() {
          _jsonOutput = 'Error generating JSON: $e';
        });
      }
    }
  }

  void _createSmallCalendar() {
    setState(() {
      _currentCalendar = CalendarWidget(
        size: CalendarSize.small,
        initialSelectedDate: DateTime.now(),
        borderRadius: 6.0,
        borderWidth: 0.5,
        hasBorder: true,
        selectedDateColor: Colors.green,
        todayColor: Colors.yellow.shade100,
      );
    });
    _generateJson();
  }

  void _createLargeCalendar() {
    setState(() {
      _currentCalendar = CalendarWidget(
        size: CalendarSize.large,
        initialSelectedDate: DateTime.now(),
        borderRadius: 24.0,
        borderWidth: 2.0,
        hasBorder: true,
        hasShadow: true,
        borderColor: Color(0xFFCCCCCC),
        selectedDateColor: Colors.purple,
        todayColor: Colors.pink.shade100,
        headerColor: Colors.white,
      );
    });
    _generateJson();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendar Widget Demo'),
        backgroundColor: Colors.blue.shade100,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RuntimeComparisonDemo(),
                ),
              );
            },
            icon: const Icon(Icons.compare_arrows),
            tooltip: 'Compare Serialization Approaches',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Control buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: _createSmallCalendar,
                  child: const Text('Small'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _createInitialCalendar,
                  child: const Text('Medium'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _createLargeCalendar,
                  child: const Text('Large'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Original calendar
            const Text(
              'Original Calendar Widget:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (_currentCalendar != null)
              Center(child: _currentCalendar!),
            
            const SizedBox(height: 24),
            
            // JSON output
            const Text(
              'Generated JSON Configuration:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              height: 300, // Fixed height 
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    _jsonOutput,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Recreated calendar
            const Text(
              'Recreated from JSON:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (_recreatedCalendar != null)
              Center(child: _recreatedCalendar!),
          ],
        ),
        ),
      ),
    );
  }
}
