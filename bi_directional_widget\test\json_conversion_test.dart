import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:bi_directional_widget/bi_directional_widget.dart';

void main() {
  group('CalendarWidget JSON Conversion Tests', () {
    test('should convert CalendarWidget to JSON', () {
      // Create a simple calendar widget
      final calendar = CalendarWidget(
        size: CalendarSize.medium,
        initialSelectedDate: DateTime(2025, 7, 21),
        borderRadius: 16.0,
        borderWidth: 1.0,
        hasBorder: true,
        selectedDateColor: Colors.blue,
        todayColor: Colors.orange,
      );

      // Test toJson conversion
      final json = calendar.toJson();
      
      // Verify basic properties
      expect(json, isA<Map<String, dynamic>>());
      expect(json['widgetType'], equals('calendar'));
      expect(json['size'], equals('medium'));
      expect(json['borderRadius'], equals(16.0));
      expect(json['borderWidth'], equals(1.0));
      expect(json['hasBorder'], equals(true));
      
      print('JSON conversion successful!');
      print('JSON keys: ${json.keys.toList()}');
    });

    test('should convert JSON back to CalendarWidget', () {
      // Create JSON data
      final json = {
        'widgetType': 'calendar',
        'size': 'medium',
        'initialSelectedDate': '2025-07-21T00:00:00.000',
        'borderRadius': 16.0,
        'borderWidth': 1.0,
        'hasBorder': true,
        'selectedDateColor': '#2196F3',
        'todayColor': '#FF9800',
      };

      // Test fromJson conversion
      final calendar = CalendarWidget.fromJson(json);
      
      // Verify properties
      expect(calendar.size, equals(CalendarSize.medium));
      expect(calendar.borderRadius, equals(16.0));
      expect(calendar.borderWidth, equals(1.0));
      expect(calendar.hasBorder, equals(true));
      
      print('JSON to Widget conversion successful!');
    });

    test('should create JSON string with pretty printing', () {
      final calendar = CalendarWidget(
        size: CalendarSize.small,
        borderRadius: 8.0,
        hasBorder: true,
      );

      final jsonString = calendar.toJsonString(prettyPrint: true);
      
      expect(jsonString, isA<String>());
      expect(jsonString.contains('{\n'), isTrue); // Should have pretty formatting
      expect(jsonString.contains('"widgetType"'), isTrue);
      
      print('JSON string length: ${jsonString.length}');
      print('First 200 characters: ${jsonString.substring(0, jsonString.length > 200 ? 200 : jsonString.length)}');
    });
  });
}
