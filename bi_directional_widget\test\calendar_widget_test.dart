import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// For testing, we'll create a minimal CalendarWidget implementation
enum CalendarSize { small, medium, large }

class CalendarEvent {
  final String title;
  final String? description;
  final Color? color;
  final DateTime? startTime;
  final DateTime? endTime;

  const CalendarEvent({
    required this.title,
    this.description,
    this.color,
    this.startTime,
    this.endTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'color': color?.value,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
    };
  }

  factory CalendarEvent.fromJson(Map<String, dynamic> json) {
    return CalendarEvent(
      title: json['title'] as String,
      description: json['description'] as String?,
      color: json['color'] != null ? Color(json['color'] as int) : null,
      startTime: json['startTime'] != null ? DateTime.parse(json['startTime']) : null,
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
    );
  }
}

class CalendarWidget extends StatefulWidget {
  final CalendarSize size;
  final DateTime? initialSelectedDate;
  final bool allowDateSelection;
  final bool allowMultipleSelection;
  final List<DateTime>? initialSelectedDates;
  final DateTime? minDate;
  final DateTime? maxDate;
  final Map<DateTime, List<CalendarEvent>>? events;
  final bool showHeader;
  final bool showNavigation;
  final bool showWeekdayLabels;
  final int firstDayOfWeek;
  final String locale;
  final double? borderRadius;
  final double? borderWidth;
  final bool hasBorder;
  final bool hasShadow;
  final Color? selectedDateColor;
  final Color? todayColor;
  final Color? headerColor;
  final double? headingFontSize;
  final double? bodyFontSize;
  final double? labelFontSize;
  final FontWeight? headingFontWeight;
  final FontWeight? bodyFontWeight;
  final FontWeight? labelFontWeight;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const CalendarWidget({
    super.key,
    this.size = CalendarSize.medium,
    this.initialSelectedDate,
    this.allowDateSelection = true,
    this.allowMultipleSelection = false,
    this.initialSelectedDates,
    this.minDate,
    this.maxDate,
    this.events,
    this.showHeader = true,
    this.showNavigation = true,
    this.showWeekdayLabels = true,
    this.firstDayOfWeek = 1,
    this.locale = 'en_US',
    this.borderRadius,
    this.borderWidth,
    this.hasBorder = true,
    this.hasShadow = false,
    this.selectedDateColor,
    this.todayColor,
    this.headerColor,
    this.headingFontSize,
    this.bodyFontSize,
    this.labelFontSize,
    this.headingFontWeight,
    this.bodyFontWeight,
    this.labelFontWeight,
    this.padding,
    this.margin,
  });

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();

  Map<String, dynamic> toJson() {
    Map<String, dynamic>? eventsJson;
    if (events != null) {
      eventsJson = {};
      events!.forEach((date, eventList) {
        final dateKey = date.toIso8601String().split('T')[0];
        eventsJson![dateKey] = eventList.map((event) => event.toJson()).toList();
      });
    }

    return {
      'widgetType': 'calendar',
      'size': size.name,
      'initialSelectedDate': initialSelectedDate?.toIso8601String(),
      'allowDateSelection': allowDateSelection,
      'allowMultipleSelection': allowMultipleSelection,
      'initialSelectedDates': initialSelectedDates?.map((date) => date.toIso8601String()).toList(),
      'minDate': minDate?.toIso8601String(),
      'maxDate': maxDate?.toIso8601String(),
      'events': eventsJson,
      'showHeader': showHeader,
      'showNavigation': showNavigation,
      'showWeekdayLabels': showWeekdayLabels,
      'firstDayOfWeek': firstDayOfWeek,
      'locale': locale,
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'hasBorder': hasBorder,
      'hasShadow': hasShadow,
      'selectedDateColor': selectedDateColor?.value,
      'todayColor': todayColor?.value,
      'headerColor': headerColor?.value,
      'headingFontSize': headingFontSize,
      'bodyFontSize': bodyFontSize,
      'labelFontSize': labelFontSize,
      'headingFontWeight': headingFontWeight?.index,
      'bodyFontWeight': bodyFontWeight?.index,
      'labelFontWeight': labelFontWeight?.index,
      'padding': padding != null ? {
        'left': padding!.left,
        'top': padding!.top,
        'right': padding!.right,
        'bottom': padding!.bottom,
      } : null,
      'margin': margin != null ? {
        'left': margin!.left,
        'top': margin!.top,
        'right': margin!.right,
        'bottom': margin!.bottom,
      } : null,
    };
  }

  factory CalendarWidget.fromJson(Map<String, dynamic> json) {
    Map<DateTime, List<CalendarEvent>>? events;
    if (json['events'] != null) {
      events = {};
      final eventsJson = json['events'] as Map<String, dynamic>;
      eventsJson.forEach((dateKey, eventListJson) {
        final date = DateTime.parse(dateKey);
        final eventList = (eventListJson as List)
            .map((eventJson) => CalendarEvent.fromJson(eventJson as Map<String, dynamic>))
            .toList();
        events![date] = eventList;
      });
    }

    return CalendarWidget(
      size: CalendarSize.values.firstWhere((e) => e.name == json['size'], orElse: () => CalendarSize.medium),
      initialSelectedDate: json['initialSelectedDate'] != null ? DateTime.parse(json['initialSelectedDate']) : null,
      allowDateSelection: json['allowDateSelection'] ?? true,
      allowMultipleSelection: json['allowMultipleSelection'] ?? false,
      initialSelectedDates: json['initialSelectedDates'] != null
          ? (json['initialSelectedDates'] as List).map((dateStr) => DateTime.parse(dateStr)).toList()
          : null,
      minDate: json['minDate'] != null ? DateTime.parse(json['minDate']) : null,
      maxDate: json['maxDate'] != null ? DateTime.parse(json['maxDate']) : null,
      events: events,
      showHeader: json['showHeader'] ?? true,
      showNavigation: json['showNavigation'] ?? true,
      showWeekdayLabels: json['showWeekdayLabels'] ?? true,
      firstDayOfWeek: json['firstDayOfWeek'] ?? 1,
      locale: json['locale'] ?? 'en_US',
      borderRadius: json['borderRadius']?.toDouble(),
      borderWidth: json['borderWidth']?.toDouble(),
      hasBorder: json['hasBorder'] ?? true,
      hasShadow: json['hasShadow'] ?? false,
      selectedDateColor: json['selectedDateColor'] != null ? Color(json['selectedDateColor'] as int) : null,
      todayColor: json['todayColor'] != null ? Color(json['todayColor'] as int) : null,
      headerColor: json['headerColor'] != null ? Color(json['headerColor'] as int) : null,
      headingFontSize: json['headingFontSize']?.toDouble(),
      bodyFontSize: json['bodyFontSize']?.toDouble(),
      labelFontSize: json['labelFontSize']?.toDouble(),
      headingFontWeight: json['headingFontWeight'] != null ? FontWeight.values[json['headingFontWeight']] : null,
      bodyFontWeight: json['bodyFontWeight'] != null ? FontWeight.values[json['bodyFontWeight']] : null,
      labelFontWeight: json['labelFontWeight'] != null ? FontWeight.values[json['labelFontWeight']] : null,
      padding: json['padding'] != null ? EdgeInsets.only(
        left: json['padding']['left']?.toDouble() ?? 0.0,
        top: json['padding']['top']?.toDouble() ?? 0.0,
        right: json['padding']['right']?.toDouble() ?? 0.0,
        bottom: json['padding']['bottom']?.toDouble() ?? 0.0,
      ) : null,
      margin: json['margin'] != null ? EdgeInsets.only(
        left: json['margin']['left']?.toDouble() ?? 0.0,
        top: json['margin']['top']?.toDouble() ?? 0.0,
        right: json['margin']['right']?.toDouble() ?? 0.0,
        bottom: json['margin']['bottom']?.toDouble() ?? 0.0,
      ) : null,
    );
  }

  String toJsonString({bool prettyPrint = false}) {
    final json = toJson();
    if (prettyPrint) {
      // Simple pretty print implementation
      return json.toString();
    }
    return json.toString();
  }

  factory CalendarWidget.fromJsonString(String jsonString) {
    // Simple implementation for testing
    // In real implementation, would use proper JSON parsing
    throw UnimplementedError('fromJsonString not implemented in test version');
  }

  bool validate({List<String>? errors}) {
    errors?.clear();
    bool isValid = true;

    if (minDate != null && maxDate != null && minDate!.isAfter(maxDate!)) {
      errors?.add('minDate cannot be after maxDate');
      isValid = false;
    }

    if (firstDayOfWeek < 0 || firstDayOfWeek > 6) {
      errors?.add('firstDayOfWeek must be between 0 and 6');
      isValid = false;
    }

    return isValid;
  }
}

class _CalendarWidgetState extends State<CalendarWidget> {
  @override
  Widget build(BuildContext context) {
    return Text('Calendar Widget - ${widget.size.name}');
  }
}

void main() {
  group('CalendarWidget Bidirectional Conversion Tests', () {
    test('should convert widget to JSON and back to widget', () {
      // Create original widget
      final originalWidget = CalendarWidget(
        size: CalendarSize.medium,
        initialSelectedDate: DateTime(2024, 6, 15),
        allowDateSelection: true,
        allowMultipleSelection: false,
        borderRadius: 16.0,
        borderWidth: 1.0,
        hasBorder: true,
        hasShadow: true,
        selectedDateColor: Colors.blue,
        todayColor: Colors.orange,
        headerColor: Colors.grey.shade100,
        events: {
          DateTime(2024, 6, 10): [
            const CalendarEvent(title: 'Meeting', color: Colors.red),
          ],
        },
      );

      // Convert to JSON
      final json = originalWidget.toJson();

      // Verify JSON contains expected fields
      expect(json['widgetType'], equals('calendar'));
      expect(json['size'], equals('medium'));
      expect(json['initialSelectedDate'], equals('2024-06-15T00:00:00.000'));
      expect(json['allowDateSelection'], equals(true));
      expect(json['allowMultipleSelection'], equals(false));
      expect(json['borderRadius'], equals(16.0));
      expect(json['borderWidth'], equals(1.0));
      expect(json['hasBorder'], equals(true));
      expect(json['hasShadow'], equals(true));

      // Convert back to widget
      final recreatedWidget = CalendarWidget.fromJson(json);

      // Verify properties are preserved
      expect(recreatedWidget.size, equals(CalendarSize.medium));
      expect(recreatedWidget.initialSelectedDate, equals(DateTime(2024, 6, 15)));
      expect(recreatedWidget.allowDateSelection, equals(true));
      expect(recreatedWidget.allowMultipleSelection, equals(false));
      expect(recreatedWidget.borderRadius, equals(16.0));
      expect(recreatedWidget.borderWidth, equals(1.0));
      expect(recreatedWidget.hasBorder, equals(true));
      expect(recreatedWidget.hasShadow, equals(true));
      expect(recreatedWidget.selectedDateColor, equals(Colors.blue));
      expect(recreatedWidget.todayColor, equals(Colors.orange));
    });

    test('should handle all calendar sizes correctly', () {
      for (final size in CalendarSize.values) {
        final widget = CalendarWidget(size: size);
        final json = widget.toJson();
        final recreated = CalendarWidget.fromJson(json);
        
        expect(recreated.size, equals(size));
        expect(json['size'], equals(size.name));
      }
    });

    test('should handle events correctly', () {
      final events = {
        DateTime(2024, 6, 10): [
          const CalendarEvent(
            title: 'Meeting',
            description: 'Team meeting',
            color: Colors.red,
          ),
          const CalendarEvent(
            title: 'Lunch',
            color: Colors.green,
          ),
        ],
        DateTime(2024, 6, 15): [
          const CalendarEvent(
            title: 'Deadline',
            description: 'Project deadline',
            color: Colors.orange,
          ),
        ],
      };

      final widget = CalendarWidget(events: events);
      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.events, isNotNull);
      expect(recreated.events!.length, equals(2));
      
      final june10Events = recreated.events![DateTime(2024, 6, 10)];
      expect(june10Events, isNotNull);
      expect(june10Events!.length, equals(2));
      expect(june10Events[0].title, equals('Meeting'));
      expect(june10Events[0].description, equals('Team meeting'));
      expect(june10Events[1].title, equals('Lunch'));
    });

    test('should handle null and default values correctly', () {
      final widget = CalendarWidget();
      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.size, equals(CalendarSize.medium));
      expect(recreated.allowDateSelection, equals(true));
      expect(recreated.allowMultipleSelection, equals(false));
      expect(recreated.showHeader, equals(true));
      expect(recreated.showNavigation, equals(true));
      expect(recreated.showWeekdayLabels, equals(true));
      expect(recreated.firstDayOfWeek, equals(1));
      expect(recreated.locale, equals('en_US'));
      expect(recreated.hasBorder, equals(true));
      expect(recreated.hasShadow, equals(false));
    });

    test('should handle date ranges correctly', () {
      final minDate = DateTime(2024, 1, 1);
      final maxDate = DateTime(2024, 12, 31);
      
      final widget = CalendarWidget(
        minDate: minDate,
        maxDate: maxDate,
      );
      
      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.minDate, equals(minDate));
      expect(recreated.maxDate, equals(maxDate));
    });

    test('should handle multiple selected dates correctly', () {
      final selectedDates = [
        DateTime(2024, 6, 10),
        DateTime(2024, 6, 15),
        DateTime(2024, 6, 20),
      ];

      final widget = CalendarWidget(
        allowMultipleSelection: true,
        initialSelectedDates: selectedDates,
      );

      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.allowMultipleSelection, equals(true));
      expect(recreated.initialSelectedDates, isNotNull);
      expect(recreated.initialSelectedDates!.length, equals(3));
      expect(recreated.initialSelectedDates![0], equals(DateTime(2024, 6, 10)));
      expect(recreated.initialSelectedDates![1], equals(DateTime(2024, 6, 15)));
      expect(recreated.initialSelectedDates![2], equals(DateTime(2024, 6, 20)));
    });

    test('should handle font properties correctly', () {
      final widget = CalendarWidget(
        headingFontSize: 18.0,
        bodyFontSize: 14.0,
        labelFontSize: 12.0,
        headingFontWeight: FontWeight.bold,
        bodyFontWeight: FontWeight.normal,
        labelFontWeight: FontWeight.w500,
      );

      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.headingFontSize, equals(18.0));
      expect(recreated.bodyFontSize, equals(14.0));
      expect(recreated.labelFontSize, equals(12.0));
      expect(recreated.headingFontWeight, equals(FontWeight.bold));
      expect(recreated.bodyFontWeight, equals(FontWeight.normal));
      expect(recreated.labelFontWeight, equals(FontWeight.w500));
    });

    test('should handle edge insets correctly', () {
      final padding = const EdgeInsets.all(16.0);
      final margin = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0);

      final widget = CalendarWidget(
        padding: padding,
        margin: margin,
      );

      final json = widget.toJson();
      final recreated = CalendarWidget.fromJson(json);

      expect(recreated.padding, equals(padding));
      expect(recreated.margin, equals(margin));
    });

    test('should validate widget configuration', () {
      final widget = CalendarWidget();
      final errors = <String>[];
      
      expect(widget.validate(errors: errors), equals(true));
      expect(errors, isEmpty);
    });

    test('should detect invalid configuration', () {
      final widget = CalendarWidget(
        minDate: DateTime(2024, 12, 31),
        maxDate: DateTime(2024, 1, 1), // Invalid: min > max
        firstDayOfWeek: 8, // Invalid: should be 0-6
      );
      
      final errors = <String>[];
      expect(widget.validate(errors: errors), equals(false));
      expect(errors, isNotEmpty);
    });

    test('should create widget from JSON string', () {
      final widget = CalendarWidget(size: CalendarSize.large);
      final jsonString = widget.toJsonString(prettyPrint: true);

      expect(jsonString, contains('calendar'));
      expect(jsonString, contains('large'));
    });
  });

  group('CalendarEvent Tests', () {
    test('should convert CalendarEvent to JSON and back', () {
      final event = CalendarEvent(
        title: 'Test Event',
        description: 'Test Description',
        color: Colors.red,
        startTime: DateTime(2024, 6, 15, 10, 0),
        endTime: DateTime(2024, 6, 15, 11, 0),
      );

      final json = event.toJson();
      final recreated = CalendarEvent.fromJson(json);

      expect(recreated.title, equals('Test Event'));
      expect(recreated.description, equals('Test Description'));
      expect(recreated.color, equals(Colors.red));
      expect(recreated.startTime, equals(DateTime(2024, 6, 15, 10, 0)));
      expect(recreated.endTime, equals(DateTime(2024, 6, 15, 11, 0)));
    });

    test('should handle minimal CalendarEvent', () {
      final event = CalendarEvent(title: 'Minimal Event');
      final json = event.toJson();
      final recreated = CalendarEvent.fromJson(json);

      expect(recreated.title, equals('Minimal Event'));
      expect(recreated.description, isNull);
      expect(recreated.color, isNull);
      expect(recreated.startTime, isNull);
      expect(recreated.endTime, isNull);
    });
  });

  // Note: Service tests would require proper package setup
  // For now, we focus on core widget functionality
}
